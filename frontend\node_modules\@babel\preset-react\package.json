{"name": "@babel/preset-react", "version": "7.0.0", "description": "Babel preset for all React plugins.", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "repository": "https://github.com/babel/babel/tree/master/packages/babel-preset-react", "main": "lib/index.js", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-transform-react-display-name": "^7.0.0", "@babel/plugin-transform-react-jsx": "^7.0.0", "@babel/plugin-transform-react-jsx-self": "^7.0.0", "@babel/plugin-transform-react-jsx-source": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0", "@babel/helper-transform-fixture-test-runner": "^7.0.0"}}