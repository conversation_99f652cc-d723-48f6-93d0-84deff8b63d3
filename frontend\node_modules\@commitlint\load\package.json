{"name": "@commitlint/load", "type": "module", "version": "19.8.1", "description": "Load shared commitlint configuration", "main": "lib/load.js", "types": "lib/load.d.ts", "files": ["lib/"], "scripts": {"deps": "dep-check", "pkg": "pkg-check --skip-import"}, "engines": {"node": ">=v18"}, "repository": {"type": "git", "url": "https://github.com/conventional-changelog/commitlint.git", "directory": "@commitlint/load"}, "bugs": {"url": "https://github.com/conventional-changelog/commitlint/issues"}, "homepage": "https://commitlint.js.org/", "keywords": ["conventional-changelog", "commitlint", "library", "core"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@commitlint/test": "^19.8.1", "@types/lodash.isplainobject": "^4.0.8", "@types/lodash.merge": "^4.6.8", "@types/lodash.uniq": "^4.5.8", "@types/node": "^18.19.17", "conventional-changelog-atom": "^4.0.0", "typescript": "^5.2.2"}, "dependencies": {"@commitlint/config-validator": "^19.8.1", "@commitlint/execute-rule": "^19.8.1", "@commitlint/resolve-extends": "^19.8.1", "@commitlint/types": "^19.8.1", "chalk": "^5.3.0", "cosmiconfig": "^9.0.0", "cosmiconfig-typescript-loader": "^6.1.0", "lodash.isplainobject": "^4.0.6", "lodash.merge": "^4.6.2", "lodash.uniq": "^4.5.0"}, "gitHead": "3c302008cabeb0b08cd246b2417a51a9d745a918"}