{"name": "@commitlint/config-conventional", "type": "module", "version": "19.8.1", "description": "Shareable commitlint config enforcing conventional commits", "main": "lib/index.js", "files": ["lib/"], "scripts": {"deps": "dep-check", "pkg": "pkg-check"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/conventional-changelog/commitlint.git", "directory": "@commitlint/config-conventional"}, "keywords": ["conventional-changelog", "commitlint", "commitlint-config", "angular"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/conventional-changelog/commitlint/issues"}, "homepage": "https://commitlint.js.org/", "engines": {"node": ">=v18"}, "devDependencies": {"@commitlint/lint": "^19.8.1", "@commitlint/utils": "^19.8.1"}, "dependencies": {"@commitlint/types": "^19.8.1", "conventional-changelog-conventionalcommits": "^7.0.2"}, "gitHead": "3c302008cabeb0b08cd246b2417a51a9d745a918"}