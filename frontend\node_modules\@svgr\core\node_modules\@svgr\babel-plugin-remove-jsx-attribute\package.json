{"name": "@svgr/babel-plugin-remove-jsx-attribute", "description": "Remove JSX attribute", "version": "8.0.0", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "repository": "https://github.com/gregberge/svgr/tree/main/packages/babel-plugin-remove-jsx-attribute", "author": "<PERSON> <<EMAIL>>", "publishConfig": {"access": "public"}, "keywords": ["babel-plugin"], "engines": {"node": ">=14"}, "homepage": "https://react-svgr.com", "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}, "license": "MIT", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "scripts": {"reset": "rm -rf dist", "build": "rollup -c ../../build/rollup.config.mjs", "prepublishOnly": "pnpm run reset && pnpm run build"}, "gitHead": "52a1079681477587ef0d842c0e78531adf2d2520"}