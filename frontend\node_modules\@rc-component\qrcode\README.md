# @rc-component/QRCode

React QRCode Component

[![NPM version][npm-image]][npm-url]
[![npm download][download-image]][download-url]
[![build status][github-actions-image]][github-actions-url]
[![Test coverage][codecov-image]][codecov-url]
[![bundle size][bundlephobia-image]][bundlephobia-url]
[![dumi][dumi-image]][dumi-url]

## Install

[![@rc-component/qrcode](https://nodei.co/npm/@rc-component/qrcode.png)](https://npmjs.org/package/@rc-component/qrcode)

## Usage

## Example

http://localhost:8001

## Development

```
npm install
npm start
```

## API

### props

## API

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |

## Test Case

```
npm test
npm run coverage
```

open coverage/ dir

## License

rc-qrcode is released under the MIT license.

### Refs

- Util part is from [qrcode.react](https://github.com/zpao/qrcode.react) under ISC license.
- QR Code generation part is from [qrcode-generator](https://www.nayuki.io/page/qr-code-generator-library) under MIT license.
