{"name": "@ant-design/icons", "version": "5.6.1", "repository": "https://github.com/ant-design/ant-design-icons/tree/master/packages/icons-react", "license": "MIT", "contributors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><<EMAIL>>", "vagusX<<EMAIL>>"], "sideEffects": false, "main": "./lib/index.js", "unpkg": "./dist/index.umd.js", "module": "./es/index.js", "typings": "./lib/index.d.ts", "scripts": {"ci": "NODE_ENV=ci npm run prepublishOnly", "clean": "./scripts/cleanup.sh", "compile": "father build", "postcompile": "npm run clean && TS_NODE_PROJECT=scripts/tsconfig.json node -r ts-node/register scripts/generate.ts --target=entry", "generate": "rimraf src/icons && TS_NODE_PROJECT=scripts/tsconfig.json node -r ts-node/register scripts/generate.ts --target=icon", "lint": "eslint src/ --ext .tsx,.ts", "prepublishOnly": "npm run generate && npm run compile && npm run lint && npm run test", "start": "dumi dev", "test": "rc-test", "test:local": "npm run generate && npm run compile && npm run test"}, "dependencies": {"@ant-design/colors": "^7.0.0", "@ant-design/icons-svg": "^4.4.0", "@babel/runtime": "^7.24.8", "classnames": "^2.2.6", "rc-util": "^5.31.1"}, "devDependencies": {"@rc-component/father-plugin": "^1.0.2", "@swc/core": "^1.3.53", "@testing-library/react": "^12", "@types/classnames": "^2.2.9", "@types/enzyme": "^3.10.3", "@types/jest": "^24.9.1", "@types/lodash": "^4.14.136", "@types/node": "^13.9.3", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^5.59.7", "@umijs/fabric": "^3.0.0", "antd": "^4.8.2", "cross-env": "^5.2.0", "dumi": "^1.1.4", "enzyme": "^3.10.0", "enzyme-adapter-react-16": "^1.15.7", "enzyme-to-json": "^3.3.5", "eslint": "^8.0.0", "eslint-plugin-jest": "^27.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unicorn": "^47.0.0", "father": "^4.0.0", "glob": "^7.1.6", "history": "^4.9.0", "lodash": "^4.17.21", "pkg-dir": "4.0.0", "prettier": "^2.2.1", "rc-test": "^7.0.15", "react": "^16.4.2", "react-dom": "^16.4.2", "rimraf": "^3.0.0", "styled-components": "^3.3.3", "ts-node": "^8.0.0", "typescript": "^5.0.0"}, "peerDependencies": {"react": ">=16.0.0", "react-dom": ">=16.0.0"}, "resolutions": {"cheerio": "1.0.0-rc.12"}, "engines": {"node": ">=8"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org"}}