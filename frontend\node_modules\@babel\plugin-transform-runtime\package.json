{"name": "@babel/plugin-transform-runtime", "version": "7.4.4", "description": "Externalise references to helpers and builtins, automatically polyfilling your code without polluting globals", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-runtime", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-module-imports": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0", "resolve": "^1.8.1", "semver": "^5.5.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.4.4", "@babel/helper-plugin-test-runner": "^7.0.0", "@babel/helpers": "^7.4.4", "@babel/plugin-transform-runtime": "^7.4.4", "@babel/preset-env": "^7.4.4", "@babel/runtime": "^7.4.4", "@babel/template": "^7.4.4", "@babel/types": "7.0.0-beta.53"}, "gitHead": "2c88694388831b1e5b88e4bbed6781eb2be1edba"}