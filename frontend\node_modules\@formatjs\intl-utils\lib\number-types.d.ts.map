{"version": 3, "file": "number-types.d.ts", "sourceRoot": "", "sources": ["../src/number-types.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,cAAc,EAAC,MAAM,sBAAsB,CAAC;AACpD,OAAO,EAAC,UAAU,EAAC,MAAM,SAAS,CAAC;AAEnC,oBAAY,oBAAoB,GAC5B,UAAU,GACV,YAAY,GACZ,aAAa,GACb,SAAS,CAAC;AAEd,oBAAY,wBAAwB,GAChC,mBAAmB,GACnB,gBAAgB,GAChB,iBAAiB,CAAC;AAEtB,MAAM,WAAW,wBAAwB;IACvC,oBAAoB,CAAC,EAAE,MAAM,CAAC;IAC9B,qBAAqB,CAAC,EAAE,MAAM,CAAC;IAC/B,qBAAqB,CAAC,EAAE,MAAM,CAAC;IAC/B,wBAAwB,CAAC,EAAE,MAAM,CAAC;IAClC,wBAAwB,CAAC,EAAE,MAAM,CAAC;CACnC;AAED,MAAM,WAAW,8BAA8B;IAC7C,oBAAoB,EAAE,MAAM,CAAC;IAC7B,wBAAwB,CAAC,EAAE,MAAM,CAAC;IAClC,wBAAwB,CAAC,EAAE,MAAM,CAAC;IAClC,YAAY,EAAE,wBAAwB,CAAC;IAEvC,qBAAqB,CAAC,EAAE,MAAM,CAAC;IAC/B,qBAAqB,CAAC,EAAE,MAAM,CAAC;IAC/B,QAAQ,CAAC,EAAE,oBAAoB,CAAC;CACjC;AAED,oBAAY,iBAAiB;IAE3B,WAAW,gBAAgB;IAC3B,aAAa,kBAAkB;IAC/B,YAAY,iBAAiB;IAC7B,YAAY,iBAAiB;IAC7B,oBAAoB,yBAAyB;IAC7C,cAAc,mBAAmB;IACjC,SAAS,cAAc;IACvB,MAAM,WAAW;IACjB,WAAW,gBAAgB;IAC3B,QAAQ,aAAa;IACrB,kBAAkB,uBAAuB;IACzC,mBAAmB,wBAAwB;IAC3C,QAAQ,aAAa;IACrB,gBAAgB,qBAAqB;IACrC,UAAU,eAAe;CAC1B;AAED,MAAM,WAAW,WAAW;IAC1B,eAAe,EAAE,MAAM,CAAC;IACxB,WAAW,EAAE,MAAM,CAAC;IACpB,eAAe,EAAE,MAAM,CAAC;CACzB;AAED,oBAAY,kBAAkB,GAAG,MAAM,CAAC,gBAAgB,EAAE,WAAW,CAAC,CAAC;AAEvE,MAAM,WAAW,eAAe;IAC9B,QAAQ,EAAE,WAAW,CAAC;IACtB,UAAU,EAAE,WAAW,CAAC;IACxB,YAAY,EAAE,kBAAkB,CAAC;IACjC,WAAW,EAAE,kBAAkB,CAAC;CACjC;AAED,MAAM,WAAW,kBAAkB;IACjC,IAAI,EAAE,eAAe,CAAC;IACtB,MAAM,EAAE,eAAe,CAAC;IACxB,KAAK,EAAE,eAAe,CAAC;IACvB,UAAU,EAAE,eAAe,CAAC;CAC7B;AAED,MAAM,WAAW,mBAAmB;IAClC,QAAQ,EAAE,kBAAkB,CAAC;IAC7B,UAAU,EAAE,kBAAkB,CAAC;CAChC;AAED,MAAM,WAAW,eAAe;IAC9B,IAAI,EAAE,mBAAmB,CAAC;IAC1B,MAAM,EAAE,mBAAmB,CAAC;IAC5B,YAAY,EAAE,mBAAmB,CAAC;IAClC,IAAI,EAAE,mBAAmB,CAAC;CAC3B;AAED,MAAM,WAAW,WAAW;IAC1B,MAAM,EAAE,kBAAkB,CAAC;IAC3B,KAAK,EAAE,kBAAkB,CAAC;IAC1B,IAAI,EAAE,kBAAkB,CAAC;CAC1B;AAED,MAAM,WAAW,SAAS;IACxB,OAAO,EAAE;QAEP,YAAY,CAAC,EAAE,MAAM,CAAC,gBAAgB,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;QAEnE,WAAW,CAAC,EAAE,MAAM,CAAC,gBAAgB,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;KACnE,CAAC;IACF,QAAQ,EAAE;QAER,YAAY,CAAC,EAAE,MAAM,CAAC,gBAAgB,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;QAEnE,WAAW,CAAC,EAAE,MAAM,CAAC,gBAAgB,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;KACnE,CAAC;IACF,OAAO,EAAE;QACP,OAAO,EAAE,MAAM,CAAC;QAChB,KAAK,EAAE,MAAM,CAAC;QACd,IAAI,EAAE,MAAM,CAAC;QACb,WAAW,EAAE,MAAM,CAAC;QACpB,QAAQ,EAAE,MAAM,CAAC;QACjB,SAAS,EAAE,MAAM,CAAC;QAClB,WAAW,EAAE,MAAM,CAAC;QACpB,sBAAsB,EAAE,MAAM,CAAC;QAC/B,QAAQ,EAAE,MAAM,CAAC;QACjB,QAAQ,EAAE,MAAM,CAAC;QACjB,GAAG,EAAE,MAAM,CAAC;QACZ,aAAa,EAAE,MAAM,CAAC;KACvB,CAAC;IACF,eAAe,EAAE,MAAM,CACrB,MAAM,EACN;QACE,cAAc,EAAE,MAAM,CAAC;QACvB,oBAAoB,EAAE,MAAM,CAAC;QAC7B,YAAY,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC;KACzC,CACF,CAAC;IACF,WAAW,EAAE,MAAM,CACjB,MAAM,EACN;QACE,UAAU,EAAE,iBAAiB,CAAC,MAAM,EAAE,CAAC,CAAC;QACxC,gBAAgB,EAAE,iBAAiB,CAAC,MAAM,EAAE,CAAC,CAAC;QAC9C,QAAQ,EAAE,iBAAiB,CAAC,MAAM,EAAE,CAAC,CAAC;KACvC,CACF,CAAC;CACH;AAED,MAAM,WAAW,uBAAuB;IACtC,OAAO,EAAE,kBAAkB,CAAC;IAC5B,OAAO,EAAE,kBAAkB,CAAC;IAC5B,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;IAC1C,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;CACnC;AAED,MAAM,WAAW,mBAAmB;IAClC,EAAE,EAAE,MAAM,EAAE,CAAC;IACb,QAAQ,EAAE,uBAAuB,CAAC;IAClC,GAAG,EAAE,SAAS,CAAC;CAChB;AAED,oBAAY,gBAAgB,GAAG,UAAU,CAAC,mBAAmB,CAAC,CAAC;AAG/D,oBAAY,mBAAmB,GAAG,UAAU,CAAC,wBAAwB,CAAC,CAAC;AAEvE,MAAM,WAAW,wBAAwB;IACvC,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;IAChC,UAAU,EAAE,MAAM,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;IACzC,OAAO,EAAE,aAAa,CAAC;IAEvB,EAAE,EAAE,MAAM,EAAE,CAAC;CACd;AAED,MAAM,WAAW,QAAQ;IACvB,WAAW,EAAE,MAAM,CAAC;IACpB,IAAI,EAAE,iBAAiB,CAAC,cAAc,CAAC,CAAC;IACxC,KAAK,EAAE,iBAAiB,CAAC,cAAc,CAAC,CAAC;IACzC,MAAM,EAAE,iBAAiB,CAAC,cAAc,CAAC,CAAC;CAC3C;AAED,MAAM,WAAW,cAAc;IAC7B,OAAO,EAAE,MAAM,CAAC;IAGhB,MAAM,EAAE,MAAM,EAAE,CAAC;CAClB;AAED,MAAM,WAAW,YAAY;IAC3B,WAAW,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC;IACvC,MAAM,EAAE,MAAM,CAAC;IACf,MAAM,EAAE,MAAM,CAAC;CAChB;AAED,oBAAY,gBAAgB,GACxB,MAAM,GACN,OAAO,GACP,QAAQ,GACR,SAAS,GACT,UAAU,GACV,WAAW,GACX,YAAY,GACZ,aAAa,GACb,cAAc,GACd,eAAe,GACf,gBAAgB,GAChB,iBAAiB,CAAC;AACtB,oBAAY,eAAe,GAAG,MAAM,CAAC;AAErC;;;;;;GAMG;AACH,MAAM,WAAW,mBAAmB;IAClC,mBAAmB,EAAE,MAAM,CAAC;IAC5B,kBAAkB,EAAE,MAAM,CAAC;CAC5B;AAED,MAAM,WAAW,eAAe;IAC9B,eAAe,EAAE,mBAAmB,CAAC;IACrC,QAAQ,EAAE,MAAM,CAAC;IACjB,UAAU,EAAE,MAAM,CAAC;IACnB,KAAK,CAAC,EAAE,MAAM,CAAC,gBAAgB,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;IAG5D,WAAW,EAAE,MAAM,CAAC;CACrB;AAED,MAAM,WAAW,WAAW;IAC1B,OAAO,EAAE,MAAM,CAAC;IAChB,KAAK,EAAE,MAAM,CAAC;IACd,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,EAAE,MAAM,CAAC;IACpB,QAAQ,EAAE,MAAM,CAAC;IACjB,SAAS,EAAE,MAAM,CAAC;IAClB,WAAW,EAAE,MAAM,CAAC;IACpB,sBAAsB,EAAE,MAAM,CAAC;IAC/B,QAAQ,EAAE,MAAM,CAAC;IACjB,QAAQ,EAAE,MAAM,CAAC;IACjB,GAAG,EAAE,MAAM,CAAC;IACZ,aAAa,EAAE,MAAM,CAAC;CACvB;AAED,MAAM,WAAW,aAAa;IAC5B,EAAE,EAAE,MAAM,EAAE,CAAC;IAEb,OAAO,EAAE,MAAM,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC;IAE9C,OAAO,EAAE,MAAM,CACb,eAAe,EACf;QACE,IAAI,EAAE,MAAM,CAAC,gBAAgB,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;QAC1D,KAAK,EAAE,MAAM,CAAC,gBAAgB,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;KAC5D,CACF,CAAC;IACF,OAAO,EAAE,MAAM,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;IACzC,QAAQ,EAAE,MAAM,CAAC,eAAe,EAAE,eAAe,CAAC,CAAC;CACpD;AAED,oBAAY,iBAAiB,CAAC,CAAC,IAAI,IAAI,CACrC,OAAO,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,EAClC,OAAO,CACR,GAAG;IACF,KAAK,EAAE,CAAC,CAAC;CACV,CAAC"}