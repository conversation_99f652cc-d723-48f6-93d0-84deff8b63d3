{"name": "@commitlint/cli", "type": "module", "version": "19.8.1", "description": "Lint your commit messages", "files": ["index.cjs", "cli.js", "lib"], "main": "index.cjs", "bin": {"commitlint": "./cli.js"}, "scripts": {"deps": "dep-check", "pkg": "pkg-check"}, "engines": {"node": ">=v18"}, "repository": {"type": "git", "url": "https://github.com/conventional-changelog/commitlint.git", "directory": "@commitlint/cli"}, "bugs": {"url": "https://github.com/conventional-changelog/commitlint/issues"}, "homepage": "https://commitlint.js.org/", "keywords": ["conventional-changelog", "commitlint", "cli"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@commitlint/test": "^19.8.1", "@commitlint/utils": "^19.8.1", "@types/lodash.merge": "^4.6.8", "@types/node": "^18.19.17", "@types/yargs": "^17.0.29", "fs-extra": "^11.0.0", "lodash.merge": "^4.6.2"}, "dependencies": {"@commitlint/format": "^19.8.1", "@commitlint/lint": "^19.8.1", "@commitlint/load": "^19.8.1", "@commitlint/read": "^19.8.1", "@commitlint/types": "^19.8.1", "tinyexec": "^1.0.0", "yargs": "^17.0.0"}, "gitHead": "3c302008cabeb0b08cd246b2417a51a9d745a918"}