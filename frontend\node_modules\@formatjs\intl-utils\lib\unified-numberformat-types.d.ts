import { LocaleData } from './types';
import { NumberInternalSlots } from './number-types';
export declare type UnifiedNumberFormatLocaleData = LocaleData<NumberInternalSlots>;
export declare type UnifiedNumberFormatOptionsLocaleMatcher = 'lookup' | 'best fit';
export declare type UnifiedNumberFormatOptionsStyle = 'decimal' | 'percent' | 'currency' | 'unit';
export declare type UnifiedNumberFormatOptionsCompactDisplay = 'short' | 'long';
export declare type UnifiedNumberFormatOptionsCurrencyDisplay = 'symbol' | 'code' | 'name' | 'narrowSymbol';
export declare type UnifiedNumberFormatOptionsCurrencySign = 'standard' | 'accounting';
export declare type UnifiedNumberFormatOptionsNotation = 'standard' | 'scientific' | 'engineering' | 'compact';
export declare type UnifiedNumberFormatOptionsSignDisplay = 'auto' | 'always' | 'never' | 'exceptZero';
export declare type UnifiedNumberFormatOptionsUnitDisplay = 'long' | 'short' | 'narrow';
//# sourceMappingURL=unified-numberformat-types.d.ts.map