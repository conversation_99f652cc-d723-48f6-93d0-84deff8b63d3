{"version": 3, "file": "index.js", "sources": ["../src/index.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/explicit-module-boundary-types */\nimport { ConfigAPI, types as t, NodePath, template } from '@babel/core'\n\nexport interface Attribute {\n  name: string\n  value?: boolean | number | string | null\n  spread?: boolean\n  literal?: boolean\n  position?: 'start' | 'end'\n}\n\nexport interface Options {\n  elements: string[]\n  attributes: Attribute[]\n}\n\nconst positionMethod = {\n  start: 'unshiftContainer',\n  end: 'pushContainer',\n} as const\n\nconst addJSXAttribute = (_: ConfigAPI, opts: Options) => {\n  function getAttributeValue({\n    literal,\n    value,\n  }: {\n    literal?: Attribute['literal']\n    value: Attribute['value']\n  }) {\n    if (typeof value === 'boolean') {\n      return t.jsxExpressionContainer(t.booleanLiteral(value))\n    }\n\n    if (typeof value === 'number') {\n      return t.jsxExpressionContainer(t.numericLiteral(value))\n    }\n\n    if (typeof value === 'string' && literal) {\n      return t.jsxExpressionContainer(\n        (template.ast(value) as t.ExpressionStatement).expression,\n      )\n    }\n\n    if (typeof value === 'string') {\n      return t.stringLiteral(value)\n    }\n\n    return null\n  }\n\n  function getAttribute({ spread, name, value, literal }: Attribute) {\n    if (spread) {\n      return t.jsxSpreadAttribute(t.identifier(name))\n    }\n\n    return t.jsxAttribute(\n      t.jsxIdentifier(name),\n      getAttributeValue({ value, literal }),\n    )\n  }\n\n  return {\n    visitor: {\n      JSXOpeningElement(path: NodePath<t.JSXOpeningElement>) {\n        if (!t.isJSXIdentifier(path.node.name)) return\n        if (!opts.elements.includes(path.node.name.name)) return\n\n        opts.attributes.forEach(\n          ({\n            name,\n            value = null,\n            spread = false,\n            literal = false,\n            position = 'end',\n          }) => {\n            const method = positionMethod[position]\n            const newAttribute = getAttribute({ spread, name, value, literal })\n            const attributes = path.get('attributes')\n\n            const isEqualAttribute = (\n              attribute: NodePath<t.JSXSpreadAttribute | t.JSXAttribute>,\n            ) => {\n              if (spread)\n                return (\n                  attribute.isJSXSpreadAttribute() &&\n                  attribute.get('argument').isIdentifier({ name })\n                )\n              return (\n                attribute.isJSXAttribute() &&\n                attribute.get('name').isJSXIdentifier({ name })\n              )\n            }\n\n            const replaced = attributes.some((attribute) => {\n              if (!isEqualAttribute(attribute)) return false\n              attribute.replaceWith(newAttribute)\n              return true\n            })\n\n            if (!replaced) {\n              path[method]('attributes', newAttribute)\n            }\n          },\n        )\n      },\n    },\n  }\n}\n\nexport default addJSXAttribute\n"], "names": ["t", "template"], "mappings": ";;;;AAgBA,MAAM,cAAiB,GAAA;AAAA,EACrB,KAAO,EAAA,kBAAA;AAAA,EACP,GAAK,EAAA,eAAA;AACP,CAAA,CAAA;AAEM,MAAA,eAAA,GAAkB,CAAC,CAAA,EAAc,IAAkB,KAAA;AACvD,EAAA,SAAS,iBAAkB,CAAA;AAAA,IACzB,OAAA;AAAA,IACA,KAAA;AAAA,GAIC,EAAA;AACD,IAAI,IAAA,OAAO,UAAU,SAAW,EAAA;AAC9B,MAAA,OAAOA,UAAE,CAAA,sBAAA,CAAuBA,UAAE,CAAA,cAAA,CAAe,KAAK,CAAC,CAAA,CAAA;AAAA,KACzD;AAEA,IAAI,IAAA,OAAO,UAAU,QAAU,EAAA;AAC7B,MAAA,OAAOA,UAAE,CAAA,sBAAA,CAAuBA,UAAE,CAAA,cAAA,CAAe,KAAK,CAAC,CAAA,CAAA;AAAA,KACzD;AAEA,IAAI,IAAA,OAAO,KAAU,KAAA,QAAA,IAAY,OAAS,EAAA;AACxC,MAAA,OAAOA,UAAE,CAAA,sBAAA;AAAA,QACNC,aAAA,CAAS,GAAI,CAAA,KAAK,CAA4B,CAAA,UAAA;AAAA,OACjD,CAAA;AAAA,KACF;AAEA,IAAI,IAAA,OAAO,UAAU,QAAU,EAAA;AAC7B,MAAO,OAAAD,UAAA,CAAE,cAAc,KAAK,CAAA,CAAA;AAAA,KAC9B;AAEA,IAAO,OAAA,IAAA,CAAA;AAAA,GACT;AAEA,EAAA,SAAS,aAAa,EAAE,MAAA,EAAQ,IAAM,EAAA,KAAA,EAAO,SAAsB,EAAA;AACjE,IAAA,IAAI,MAAQ,EAAA;AACV,MAAA,OAAOA,UAAE,CAAA,kBAAA,CAAmBA,UAAE,CAAA,UAAA,CAAW,IAAI,CAAC,CAAA,CAAA;AAAA,KAChD;AAEA,IAAA,OAAOA,UAAE,CAAA,YAAA;AAAA,MACPA,UAAA,CAAE,cAAc,IAAI,CAAA;AAAA,MACpB,iBAAkB,CAAA,EAAE,KAAO,EAAA,OAAA,EAAS,CAAA;AAAA,KACtC,CAAA;AAAA,GACF;AAEA,EAAO,OAAA;AAAA,IACL,OAAS,EAAA;AAAA,MACP,kBAAkB,IAAqC,EAAA;AACrD,QAAA,IAAI,CAACA,UAAA,CAAE,eAAgB,CAAA,IAAA,CAAK,KAAK,IAAI,CAAA;AAAG,UAAA,OAAA;AACxC,QAAA,IAAI,CAAC,IAAK,CAAA,QAAA,CAAS,SAAS,IAAK,CAAA,IAAA,CAAK,KAAK,IAAI,CAAA;AAAG,UAAA,OAAA;AAElD,QAAA,IAAA,CAAK,UAAW,CAAA,OAAA;AAAA,UACd,CAAC;AAAA,YACC,IAAA;AAAA,YACA,KAAQ,GAAA,IAAA;AAAA,YACR,MAAS,GAAA,KAAA;AAAA,YACT,OAAU,GAAA,KAAA;AAAA,YACV,QAAW,GAAA,KAAA;AAAA,WACP,KAAA;AACJ,YAAA,MAAM,SAAS,cAAe,CAAA,QAAA,CAAA,CAAA;AAC9B,YAAA,MAAM,eAAe,YAAa,CAAA,EAAE,QAAQ,IAAM,EAAA,KAAA,EAAO,SAAS,CAAA,CAAA;AAClE,YAAM,MAAA,UAAA,GAAa,IAAK,CAAA,GAAA,CAAI,YAAY,CAAA,CAAA;AAExC,YAAM,MAAA,gBAAA,GAAmB,CACvB,SACG,KAAA;AACH,cAAI,IAAA,MAAA;AACF,gBACE,OAAA,SAAA,CAAU,oBAAqB,EAAA,IAC/B,SAAU,CAAA,GAAA,CAAI,UAAU,CAAE,CAAA,YAAA,CAAa,EAAE,IAAA,EAAM,CAAA,CAAA;AAEnD,cACE,OAAA,SAAA,CAAU,cAAe,EAAA,IACzB,SAAU,CAAA,GAAA,CAAI,MAAM,CAAE,CAAA,eAAA,CAAgB,EAAE,IAAA,EAAM,CAAA,CAAA;AAAA,aAElD,CAAA;AAEA,YAAA,MAAM,QAAW,GAAA,UAAA,CAAW,IAAK,CAAA,CAAC,SAAc,KAAA;AAC9C,cAAI,IAAA,CAAC,iBAAiB,SAAS,CAAA;AAAG,gBAAO,OAAA,KAAA,CAAA;AACzC,cAAA,SAAA,CAAU,YAAY,YAAY,CAAA,CAAA;AAClC,cAAO,OAAA,IAAA,CAAA;AAAA,aACR,CAAA,CAAA;AAED,YAAA,IAAI,CAAC,QAAU,EAAA;AACb,cAAK,IAAA,CAAA,MAAA,CAAA,CAAQ,cAAc,YAAY,CAAA,CAAA;AAAA,aACzC;AAAA,WACF;AAAA,SACF,CAAA;AAAA,OACF;AAAA,KACF;AAAA,GACF,CAAA;AACF;;;;"}