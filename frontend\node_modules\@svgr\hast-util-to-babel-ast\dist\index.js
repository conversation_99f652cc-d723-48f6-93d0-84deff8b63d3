'use strict';

var t = require('@babel/types');
var entities = require('entities');

function _interopNamespace(e) {
  if (e && e.__esModule) return e;
  var n = Object.create(null);
  if (e) {
    Object.keys(e).forEach(function (k) {
      if (k !== 'default') {
        var d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: function () { return e[k]; }
        });
      }
    });
  }
  n["default"] = e;
  return Object.freeze(n);
}

var t__namespace = /*#__PURE__*/_interopNamespace(t);

const one = (h, node, parent) => {
  const type = node && node.type;
  const fn = h.handlers[type];
  if (!type) {
    throw new Error(`Expected node, got \`${node}\``);
  }
  if (!fn) {
    throw new Error(`Node of type ${type} is unknown`);
  }
  return fn(h, node, parent);
};

const all = (helpers, parent) => {
  const nodes = parent.children || [];
  const { length } = nodes;
  const values = [];
  let index = -1;
  while (++index < length) {
    const node = nodes[index];
    if (typeof node !== "string") {
      const result = one(helpers, node, parent);
      values.push(result);
    }
  }
  return values.filter(Boolean);
};

const isNumeric = (value) => {
  return !Number.isNaN(value - parseFloat(value));
};
const hyphenToCamelCase = (string) => {
  return string.replace(/-(.)/g, (_, chr) => chr.toUpperCase());
};
const trimEnd = (haystack, needle) => {
  return haystack.endsWith(needle) ? haystack.slice(0, -needle.length) : haystack;
};
const KEBAB_REGEX = /[A-Z\u00C0-\u00D6\u00D8-\u00DE]/g;
const kebabCase = (str) => {
  return str.replace(KEBAB_REGEX, (match) => `-${match.toLowerCase()}`);
};
const SPACES_REGEXP = /[\t\r\n\u0085\u2028\u2029]+/g;
const replaceSpaces = (str) => {
  return str.replace(SPACES_REGEXP, " ");
};

const PX_REGEX = /^\d+px$/;
const MS_REGEX = /^-ms-/;
const VAR_REGEX = /^--/;
const isConvertiblePixelValue = (value) => {
  return PX_REGEX.test(value);
};
const formatKey = (key) => {
  if (VAR_REGEX.test(key)) {
    return t__namespace.stringLiteral(key);
  }
  key = key.toLowerCase();
  if (MS_REGEX.test(key))
    key = key.substr(1);
  return t__namespace.identifier(hyphenToCamelCase(key));
};
const formatValue = (value) => {
  if (isNumeric(value))
    return t__namespace.numericLiteral(Number(value));
  if (isConvertiblePixelValue(value))
    return t__namespace.numericLiteral(Number(trimEnd(value, "px")));
  return t__namespace.stringLiteral(value);
};
const stringToObjectStyle = (rawStyle) => {
  const entries = rawStyle.split(";");
  const properties = [];
  let index = -1;
  while (++index < entries.length) {
    const entry = entries[index];
    const style = entry.trim();
    const firstColon = style.indexOf(":");
    const value = style.substr(firstColon + 1).trim();
    const key = style.substr(0, firstColon);
    if (key !== "") {
      const property = t__namespace.objectProperty(formatKey(key), formatValue(value));
      properties.push(property);
    }
  }
  return t__namespace.objectExpression(properties);
};

const ATTRIBUTE_MAPPING = {
  accept: "accept",
  acceptcharset: "acceptCharset",
  "accept-charset": "acceptCharset",
  accesskey: "accessKey",
  action: "action",
  allowfullscreen: "allowFullScreen",
  alt: "alt",
  as: "as",
  async: "async",
  autocapitalize: "autoCapitalize",
  autocomplete: "autoComplete",
  autocorrect: "autoCorrect",
  autofocus: "autoFocus",
  autoplay: "autoPlay",
  autosave: "autoSave",
  capture: "capture",
  cellpadding: "cellPadding",
  cellspacing: "cellSpacing",
  challenge: "challenge",
  charset: "charSet",
  checked: "checked",
  children: "children",
  cite: "cite",
  class: "className",
  classid: "classID",
  classname: "className",
  cols: "cols",
  colspan: "colSpan",
  content: "content",
  contenteditable: "contentEditable",
  contextmenu: "contextMenu",
  controls: "controls",
  controlslist: "controlsList",
  coords: "coords",
  crossorigin: "crossOrigin",
  dangerouslysetinnerhtml: "dangerouslySetInnerHTML",
  data: "data",
  datetime: "dateTime",
  default: "default",
  defaultchecked: "defaultChecked",
  defaultvalue: "defaultValue",
  defer: "defer",
  dir: "dir",
  disabled: "disabled",
  download: "download",
  draggable: "draggable",
  enctype: "encType",
  for: "htmlFor",
  form: "form",
  formmethod: "formMethod",
  formaction: "formAction",
  formenctype: "formEncType",
  formnovalidate: "formNoValidate",
  formtarget: "formTarget",
  frameborder: "frameBorder",
  headers: "headers",
  height: "height",
  hidden: "hidden",
  high: "high",
  href: "href",
  hreflang: "hrefLang",
  htmlfor: "htmlFor",
  httpequiv: "httpEquiv",
  "http-equiv": "httpEquiv",
  icon: "icon",
  id: "id",
  innerhtml: "innerHTML",
  inputmode: "inputMode",
  integrity: "integrity",
  is: "is",
  itemid: "itemID",
  itemprop: "itemProp",
  itemref: "itemRef",
  itemscope: "itemScope",
  itemtype: "itemType",
  keyparams: "keyParams",
  keytype: "keyType",
  kind: "kind",
  label: "label",
  lang: "lang",
  list: "list",
  loop: "loop",
  low: "low",
  manifest: "manifest",
  marginwidth: "marginWidth",
  marginheight: "marginHeight",
  max: "max",
  maxlength: "maxLength",
  media: "media",
  mediagroup: "mediaGroup",
  method: "method",
  min: "min",
  minlength: "minLength",
  multiple: "multiple",
  muted: "muted",
  name: "name",
  nomodule: "noModule",
  nonce: "nonce",
  novalidate: "noValidate",
  open: "open",
  optimum: "optimum",
  pattern: "pattern",
  placeholder: "placeholder",
  playsinline: "playsInline",
  poster: "poster",
  preload: "preload",
  profile: "profile",
  radiogroup: "radioGroup",
  readonly: "readOnly",
  referrerpolicy: "referrerPolicy",
  rel: "rel",
  required: "required",
  reversed: "reversed",
  role: "role",
  rows: "rows",
  rowspan: "rowSpan",
  sandbox: "sandbox",
  scope: "scope",
  scoped: "scoped",
  scrolling: "scrolling",
  seamless: "seamless",
  selected: "selected",
  shape: "shape",
  size: "size",
  sizes: "sizes",
  span: "span",
  spellcheck: "spellCheck",
  src: "src",
  srcdoc: "srcDoc",
  srclang: "srcLang",
  srcset: "srcSet",
  start: "start",
  step: "step",
  style: "style",
  summary: "summary",
  tabindex: "tabIndex",
  target: "target",
  title: "title",
  type: "type",
  usemap: "useMap",
  value: "value",
  width: "width",
  wmode: "wmode",
  wrap: "wrap",
  about: "about",
  accentheight: "accentHeight",
  "accent-height": "accentHeight",
  accumulate: "accumulate",
  additive: "additive",
  alignmentbaseline: "alignmentBaseline",
  "alignment-baseline": "alignmentBaseline",
  allowreorder: "allowReorder",
  alphabetic: "alphabetic",
  amplitude: "amplitude",
  arabicform: "arabicForm",
  "arabic-form": "arabicForm",
  ascent: "ascent",
  attributename: "attributeName",
  attributetype: "attributeType",
  autoreverse: "autoReverse",
  azimuth: "azimuth",
  basefrequency: "baseFrequency",
  baselineshift: "baselineShift",
  "baseline-shift": "baselineShift",
  baseprofile: "baseProfile",
  bbox: "bbox",
  begin: "begin",
  bias: "bias",
  by: "by",
  calcmode: "calcMode",
  capheight: "capHeight",
  "cap-height": "capHeight",
  clip: "clip",
  clippath: "clipPath",
  "clip-path": "clipPath",
  clippathunits: "clipPathUnits",
  cliprule: "clipRule",
  "clip-rule": "clipRule",
  color: "color",
  colorinterpolation: "colorInterpolation",
  "color-interpolation": "colorInterpolation",
  colorinterpolationfilters: "colorInterpolationFilters",
  "color-interpolation-filters": "colorInterpolationFilters",
  colorprofile: "colorProfile",
  "color-profile": "colorProfile",
  colorrendering: "colorRendering",
  "color-rendering": "colorRendering",
  contentscripttype: "contentScriptType",
  contentstyletype: "contentStyleType",
  cursor: "cursor",
  cx: "cx",
  cy: "cy",
  d: "d",
  datatype: "datatype",
  decelerate: "decelerate",
  descent: "descent",
  diffuseconstant: "diffuseConstant",
  direction: "direction",
  display: "display",
  divisor: "divisor",
  dominantbaseline: "dominantBaseline",
  "dominant-baseline": "dominantBaseline",
  dur: "dur",
  dx: "dx",
  dy: "dy",
  edgemode: "edgeMode",
  elevation: "elevation",
  enablebackground: "enableBackground",
  "enable-background": "enableBackground",
  end: "end",
  exponent: "exponent",
  externalresourcesrequired: "externalResourcesRequired",
  fill: "fill",
  fillopacity: "fillOpacity",
  "fill-opacity": "fillOpacity",
  fillrule: "fillRule",
  "fill-rule": "fillRule",
  filter: "filter",
  filterres: "filterRes",
  filterunits: "filterUnits",
  floodopacity: "floodOpacity",
  "flood-opacity": "floodOpacity",
  floodcolor: "floodColor",
  "flood-color": "floodColor",
  focusable: "focusable",
  fontfamily: "fontFamily",
  "font-family": "fontFamily",
  fontsize: "fontSize",
  "font-size": "fontSize",
  fontsizeadjust: "fontSizeAdjust",
  "font-size-adjust": "fontSizeAdjust",
  fontstretch: "fontStretch",
  "font-stretch": "fontStretch",
  fontstyle: "fontStyle",
  "font-style": "fontStyle",
  fontvariant: "fontVariant",
  "font-variant": "fontVariant",
  fontweight: "fontWeight",
  "font-weight": "fontWeight",
  format: "format",
  from: "from",
  fx: "fx",
  fy: "fy",
  g1: "g1",
  g2: "g2",
  glyphname: "glyphName",
  "glyph-name": "glyphName",
  glyphorientationhorizontal: "glyphOrientationHorizontal",
  "glyph-orientation-horizontal": "glyphOrientationHorizontal",
  glyphorientationvertical: "glyphOrientationVertical",
  "glyph-orientation-vertical": "glyphOrientationVertical",
  glyphref: "glyphRef",
  gradienttransform: "gradientTransform",
  gradientunits: "gradientUnits",
  hanging: "hanging",
  horizadvx: "horizAdvX",
  "horiz-adv-x": "horizAdvX",
  horizoriginx: "horizOriginX",
  "horiz-origin-x": "horizOriginX",
  ideographic: "ideographic",
  imagerendering: "imageRendering",
  "image-rendering": "imageRendering",
  in2: "in2",
  in: "in",
  inlist: "inlist",
  intercept: "intercept",
  k1: "k1",
  k2: "k2",
  k3: "k3",
  k4: "k4",
  k: "k",
  kernelmatrix: "kernelMatrix",
  kernelunitlength: "kernelUnitLength",
  kerning: "kerning",
  keypoints: "keyPoints",
  keysplines: "keySplines",
  keytimes: "keyTimes",
  lengthadjust: "lengthAdjust",
  letterspacing: "letterSpacing",
  "letter-spacing": "letterSpacing",
  lightingcolor: "lightingColor",
  "lighting-color": "lightingColor",
  limitingconeangle: "limitingConeAngle",
  local: "local",
  markerend: "markerEnd",
  "marker-end": "markerEnd",
  markerheight: "markerHeight",
  markermid: "markerMid",
  "marker-mid": "markerMid",
  markerstart: "markerStart",
  "marker-start": "markerStart",
  markerunits: "markerUnits",
  markerwidth: "markerWidth",
  mask: "mask",
  maskcontentunits: "maskContentUnits",
  maskunits: "maskUnits",
  mathematical: "mathematical",
  mode: "mode",
  numoctaves: "numOctaves",
  offset: "offset",
  opacity: "opacity",
  operator: "operator",
  order: "order",
  orient: "orient",
  orientation: "orientation",
  origin: "origin",
  overflow: "overflow",
  overlineposition: "overlinePosition",
  "overline-position": "overlinePosition",
  overlinethickness: "overlineThickness",
  "overline-thickness": "overlineThickness",
  paintorder: "paintOrder",
  "paint-order": "paintOrder",
  panose1: "panose1",
  "panose-1": "panose1",
  pathlength: "pathLength",
  patterncontentunits: "patternContentUnits",
  patterntransform: "patternTransform",
  patternunits: "patternUnits",
  pointerevents: "pointerEvents",
  "pointer-events": "pointerEvents",
  points: "points",
  pointsatx: "pointsAtX",
  pointsaty: "pointsAtY",
  pointsatz: "pointsAtZ",
  prefix: "prefix",
  preservealpha: "preserveAlpha",
  preserveaspectratio: "preserveAspectRatio",
  primitiveunits: "primitiveUnits",
  property: "property",
  r: "r",
  radius: "radius",
  refx: "refX",
  refy: "refY",
  renderingintent: "renderingIntent",
  "rendering-intent": "renderingIntent",
  repeatcount: "repeatCount",
  repeatdur: "repeatDur",
  requiredextensions: "requiredExtensions",
  requiredfeatures: "requiredFeatures",
  resource: "resource",
  restart: "restart",
  result: "result",
  results: "results",
  rotate: "rotate",
  rx: "rx",
  ry: "ry",
  scale: "scale",
  security: "security",
  seed: "seed",
  shaperendering: "shapeRendering",
  "shape-rendering": "shapeRendering",
  slope: "slope",
  spacing: "spacing",
  specularconstant: "specularConstant",
  specularexponent: "specularExponent",
  speed: "speed",
  spreadmethod: "spreadMethod",
  startoffset: "startOffset",
  stddeviation: "stdDeviation",
  stemh: "stemh",
  stemv: "stemv",
  stitchtiles: "stitchTiles",
  stopcolor: "stopColor",
  "stop-color": "stopColor",
  stopopacity: "stopOpacity",
  "stop-opacity": "stopOpacity",
  strikethroughposition: "strikethroughPosition",
  "strikethrough-position": "strikethroughPosition",
  strikethroughthickness: "strikethroughThickness",
  "strikethrough-thickness": "strikethroughThickness",
  string: "string",
  stroke: "stroke",
  strokedasharray: "strokeDasharray",
  "stroke-dasharray": "strokeDasharray",
  strokedashoffset: "strokeDashoffset",
  "stroke-dashoffset": "strokeDashoffset",
  strokelinecap: "strokeLinecap",
  "stroke-linecap": "strokeLinecap",
  strokelinejoin: "strokeLinejoin",
  "stroke-linejoin": "strokeLinejoin",
  strokemiterlimit: "strokeMiterlimit",
  "stroke-miterlimit": "strokeMiterlimit",
  strokewidth: "strokeWidth",
  "stroke-width": "strokeWidth",
  strokeopacity: "strokeOpacity",
  "stroke-opacity": "strokeOpacity",
  suppresscontenteditablewarning: "suppressContentEditableWarning",
  suppresshydrationwarning: "suppressHydrationWarning",
  surfacescale: "surfaceScale",
  systemlanguage: "systemLanguage",
  tablevalues: "tableValues",
  targetx: "targetX",
  targety: "targetY",
  textanchor: "textAnchor",
  "text-anchor": "textAnchor",
  textdecoration: "textDecoration",
  "text-decoration": "textDecoration",
  textlength: "textLength",
  textrendering: "textRendering",
  "text-rendering": "textRendering",
  to: "to",
  transform: "transform",
  typeof: "typeof",
  u1: "u1",
  u2: "u2",
  underlineposition: "underlinePosition",
  "underline-position": "underlinePosition",
  underlinethickness: "underlineThickness",
  "underline-thickness": "underlineThickness",
  unicode: "unicode",
  unicodebidi: "unicodeBidi",
  "unicode-bidi": "unicodeBidi",
  unicoderange: "unicodeRange",
  "unicode-range": "unicodeRange",
  unitsperem: "unitsPerEm",
  "units-per-em": "unitsPerEm",
  unselectable: "unselectable",
  valphabetic: "vAlphabetic",
  "v-alphabetic": "vAlphabetic",
  values: "values",
  vectoreffect: "vectorEffect",
  "vector-effect": "vectorEffect",
  version: "version",
  vertadvy: "vertAdvY",
  "vert-adv-y": "vertAdvY",
  vertoriginx: "vertOriginX",
  "vert-origin-x": "vertOriginX",
  vertoriginy: "vertOriginY",
  "vert-origin-y": "vertOriginY",
  vhanging: "vHanging",
  "v-hanging": "vHanging",
  videographic: "vIdeographic",
  "v-ideographic": "vIdeographic",
  viewbox: "viewBox",
  viewtarget: "viewTarget",
  visibility: "visibility",
  vmathematical: "vMathematical",
  "v-mathematical": "vMathematical",
  vocab: "vocab",
  widths: "widths",
  wordspacing: "wordSpacing",
  "word-spacing": "wordSpacing",
  writingmode: "writingMode",
  "writing-mode": "writingMode",
  x1: "x1",
  x2: "x2",
  x: "x",
  xchannelselector: "xChannelSelector",
  xheight: "xHeight",
  "x-height": "xHeight",
  xlinkactuate: "xlinkActuate",
  "xlink:actuate": "xlinkActuate",
  xlinkarcrole: "xlinkArcrole",
  "xlink:arcrole": "xlinkArcrole",
  xlinkhref: "xlinkHref",
  "xlink:href": "xlinkHref",
  xlinkrole: "xlinkRole",
  "xlink:role": "xlinkRole",
  xlinkshow: "xlinkShow",
  "xlink:show": "xlinkShow",
  xlinktitle: "xlinkTitle",
  "xlink:title": "xlinkTitle",
  xlinktype: "xlinkType",
  "xlink:type": "xlinkType",
  xmlbase: "xmlBase",
  "xml:base": "xmlBase",
  xmllang: "xmlLang",
  "xml:lang": "xmlLang",
  xmlns: "xmlns",
  "xml:space": "xmlSpace",
  xmlnsxlink: "xmlnsXlink",
  "xmlns:xlink": "xmlnsXlink",
  xmlspace: "xmlSpace",
  y1: "y1",
  y2: "y2",
  y: "y",
  ychannelselector: "yChannelSelector",
  z: "z",
  zoomandpan: "zoomAndPan"
};
const ELEMENT_ATTRIBUTE_MAPPING = {
  input: {
    checked: "defaultChecked",
    value: "defaultValue",
    maxlength: "maxLength"
  },
  form: {
    enctype: "encType"
  }
};
const ELEMENT_TAG_NAME_MAPPING = {
  a: "a",
  altglyph: "altGlyph",
  altglyphdef: "altGlyphDef",
  altglyphitem: "altGlyphItem",
  animate: "animate",
  animatecolor: "animateColor",
  animatemotion: "animateMotion",
  animatetransform: "animateTransform",
  audio: "audio",
  canvas: "canvas",
  circle: "circle",
  clippath: "clipPath",
  "color-profile": "colorProfile",
  cursor: "cursor",
  defs: "defs",
  desc: "desc",
  discard: "discard",
  ellipse: "ellipse",
  feblend: "feBlend",
  fecolormatrix: "feColorMatrix",
  fecomponenttransfer: "feComponentTransfer",
  fecomposite: "feComposite",
  feconvolvematrix: "feConvolveMatrix",
  fediffuselighting: "feDiffuseLighting",
  fedisplacementmap: "feDisplacementMap",
  fedistantlight: "feDistantLight",
  fedropshadow: "feDropShadow",
  feflood: "feFlood",
  fefunca: "feFuncA",
  fefuncb: "feFuncB",
  fefuncg: "feFuncG",
  fefuncr: "feFuncR",
  fegaussianblur: "feGaussianBlur",
  feimage: "feImage",
  femerge: "feMerge",
  femergenode: "feMergeNode",
  femorphology: "feMorphology",
  feoffset: "feOffset",
  fepointlight: "fePointLight",
  fespecularlighting: "feSpecularLighting",
  fespotlight: "feSpotLight",
  fetile: "feTile",
  feturbulence: "feTurbulence",
  filter: "filter",
  font: "font",
  "font-face": "fontFace",
  "font-face-format": "fontFaceFormat",
  "font-face-name": "fontFaceName",
  "font-face-src": "fontFaceSrc",
  "font-face-uri": "fontFaceUri",
  foreignobject: "foreignObject",
  g: "g",
  glyph: "glyph",
  glyphref: "glyphRef",
  hatch: "hatch",
  hatchpath: "hatchpath",
  hkern: "hkern",
  iframe: "iframe",
  image: "image",
  line: "line",
  lineargradient: "linearGradient",
  marker: "marker",
  mask: "mask",
  mesh: "mesh",
  meshgradient: "meshgradient",
  meshpatch: "meshpatch",
  meshrow: "meshrow",
  metadata: "metadata",
  "missing-glyph": "missingGlyph",
  mpath: "mpath",
  path: "path",
  pattern: "pattern",
  polygon: "polygon",
  polyline: "polyline",
  radialgradient: "radialGradient",
  rect: "rect",
  script: "script",
  set: "set",
  solidcolor: "solidcolor",
  stop: "stop",
  style: "style",
  svg: "svg",
  switch: "switch",
  symbol: "symbol",
  text: "text",
  textpath: "textPath",
  title: "title",
  tref: "tref",
  tspan: "tspan",
  unknown: "unknown",
  use: "use",
  video: "video",
  view: "view",
  vkern: "vkern"
};

const convertAriaAttribute = (kebabKey) => {
  const [aria, ...parts] = kebabKey.split("-");
  return `${aria}-${parts.join("").toLowerCase()}`;
};
const getKey = (key, node) => {
  const lowerCaseKey = key.toLowerCase();
  const mappedElementAttribute = ELEMENT_ATTRIBUTE_MAPPING[node.name] && ELEMENT_ATTRIBUTE_MAPPING[node.name][lowerCaseKey];
  const mappedAttribute = ATTRIBUTE_MAPPING[lowerCaseKey];
  if (mappedElementAttribute || mappedAttribute) {
    return t__namespace.jsxIdentifier(mappedElementAttribute || mappedAttribute);
  }
  const kebabKey = kebabCase(key);
  if (kebabKey.startsWith("aria-")) {
    return t__namespace.jsxIdentifier(convertAriaAttribute(kebabKey));
  }
  if (kebabKey.startsWith("data-")) {
    return t__namespace.jsxIdentifier(kebabKey);
  }
  return t__namespace.jsxIdentifier(key);
};
const getValue = (key, value) => {
  if (Array.isArray(value)) {
    return t__namespace.stringLiteral(replaceSpaces(value.join(" ")));
  }
  if (key === "style") {
    return t__namespace.jsxExpressionContainer(stringToObjectStyle(value));
  }
  if (typeof value === "number" || isNumeric(value)) {
    return t__namespace.jsxExpressionContainer(t__namespace.numericLiteral(Number(value)));
  }
  return t__namespace.stringLiteral(replaceSpaces(value));
};
const getAttributes = (node) => {
  if (!node.properties)
    return [];
  const keys = Object.keys(node.properties);
  const attributes = [];
  let index = -1;
  while (++index < keys.length) {
    const key = keys[index];
    const value = node.properties[key];
    const attribute = t__namespace.jsxAttribute(getKey(key, node), getValue(key, value));
    attributes.push(attribute);
  }
  return attributes;
};

const root = (h, node) => t__namespace.program(all(h, node));
const comment = (_, node, parent) => {
  if (parent.type === "root" || !node.value)
    return null;
  const expression = t__namespace.jsxEmptyExpression();
  t__namespace.addComment(expression, "inner", node.value);
  return t__namespace.jsxExpressionContainer(expression);
};
const SPACE_REGEX = /^\s+$/;
const text = (h, node, parent) => {
  if (parent.type === "root")
    return null;
  if (typeof node.value === "string" && SPACE_REGEX.test(node.value))
    return null;
  return t__namespace.jsxExpressionContainer(
    t__namespace.stringLiteral(entities.decodeXML(String(node.value)))
  );
};
const element = (h, node, parent) => {
  if (!node.tagName)
    return null;
  const children = all(h, node);
  const selfClosing = children.length === 0;
  const name = ELEMENT_TAG_NAME_MAPPING[node.tagName] || node.tagName;
  const openingElement = t__namespace.jsxOpeningElement(
    t__namespace.jsxIdentifier(name),
    getAttributes(node),
    selfClosing
  );
  const closingElement = !selfClosing ? t__namespace.jsxClosingElement(t__namespace.jsxIdentifier(name)) : null;
  const jsxElement = t__namespace.jsxElement(openingElement, closingElement, children);
  if (parent.type === "root") {
    return t__namespace.expressionStatement(jsxElement);
  }
  return jsxElement;
};

var handlers = /*#__PURE__*/Object.freeze({
  __proto__: null,
  root: root,
  comment: comment,
  text: text,
  element: element
});

const helpers = { handlers };

const toBabelAST = (tree) => root(helpers, tree);

module.exports = toBabelAST;
//# sourceMappingURL=index.js.map
