{"name": "@tanstack/match-sorter-utils", "version": "8.19.4", "description": "A fork of match-sorter with separated filtering and sorting phases", "contributors": ["Kent <PERSON>", "<PERSON>"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/TanStack/table.git", "directory": "packages/match-sorter-utils"}, "homepage": "https://tanstack.com/table", "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "keywords": ["react", "table", "match-sorter", "utils", "split", "separate", "datagrid"], "type": "commonjs", "module": "build/lib/index.esm.js", "main": "build/lib/index.js", "types": "build/lib/index.d.ts", "exports": {".": {"types": "./build/lib/index.d.ts", "import": "./build/lib/index.mjs", "default": "./build/lib/index.js"}, "./package.json": "./package.json"}, "sideEffects": false, "engines": {"node": ">=12"}, "files": ["build/lib/*", "build/umd/*", "src"], "dependencies": {"remove-accents": "0.5.0"}, "scripts": {}}