!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("hoist-non-react-statics")):"function"==typeof define&&define.amd?define(["exports","react","hoist-non-react-statics"],t):t((e=e||self).loadable={},e.<PERSON>,e.hoistNonReactStatics)}(this,(function(e,t,r){"use strict";function n(e,t){if(!e){var r=new Error("loadable: "+t);throw r.framesToPop=1,r.name="Invariant Violation",r}}function o(e){console.warn("loadable: "+e)}t=t&&t.hasOwnProperty("default")?t.default:t,r=r&&r.hasOwnProperty("default")?r.default:r;var a=t.createContext();function i(e){return e+"__LOADABLE_REQUIRED_CHUNKS__"}var s=Object.freeze({__proto__:null,getRequiredChunkKey:i,invariant:n,Context:a});function c(e,t){if(null==e)return{};var r,n,o={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}function u(){return(u=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function l(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function f(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function d(e,t){return e(t={exports:{}},t.exports),t.exports}var p=d((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var r="function"==typeof Symbol&&Symbol.for,n=r?Symbol.for("react.element"):60103,o=r?Symbol.for("react.portal"):60106,a=r?Symbol.for("react.fragment"):60107,i=r?Symbol.for("react.strict_mode"):60108,s=r?Symbol.for("react.profiler"):60114,c=r?Symbol.for("react.provider"):60109,u=r?Symbol.for("react.context"):60110,l=r?Symbol.for("react.async_mode"):60111,f=r?Symbol.for("react.concurrent_mode"):60111,d=r?Symbol.for("react.forward_ref"):60112,p=r?Symbol.for("react.suspense"):60113,y=r?Symbol.for("react.suspense_list"):60120,m=r?Symbol.for("react.memo"):60115,h=r?Symbol.for("react.lazy"):60116,v=r?Symbol.for("react.fundamental"):60117,b=r?Symbol.for("react.responder"):60118,_=r?Symbol.for("react.scope"):60119;function C(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case l:case f:case a:case s:case i:case p:return e;default:switch(e=e&&e.$$typeof){case u:case d:case h:case m:case c:return e;default:return t}}case o:return t}}}function S(e){return C(e)===f}t.typeOf=C,t.AsyncMode=l,t.ConcurrentMode=f,t.ContextConsumer=u,t.ContextProvider=c,t.Element=n,t.ForwardRef=d,t.Fragment=a,t.Lazy=h,t.Memo=m,t.Portal=o,t.Profiler=s,t.StrictMode=i,t.Suspense=p,t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===a||e===f||e===s||e===i||e===p||e===y||"object"==typeof e&&null!==e&&(e.$$typeof===h||e.$$typeof===m||e.$$typeof===c||e.$$typeof===u||e.$$typeof===d||e.$$typeof===v||e.$$typeof===b||e.$$typeof===_)},t.isAsyncMode=function(e){return S(e)||C(e)===l},t.isConcurrentMode=S,t.isContextConsumer=function(e){return C(e)===u},t.isContextProvider=function(e){return C(e)===c},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===n},t.isForwardRef=function(e){return C(e)===d},t.isFragment=function(e){return C(e)===a},t.isLazy=function(e){return C(e)===h},t.isMemo=function(e){return C(e)===m},t.isPortal=function(e){return C(e)===o},t.isProfiler=function(e){return C(e)===s},t.isStrictMode=function(e){return C(e)===i},t.isSuspense=function(e){return C(e)===p}}));f(p);p.typeOf,p.AsyncMode,p.ConcurrentMode,p.ContextConsumer,p.ContextProvider,p.Element,p.ForwardRef,p.Fragment,p.Lazy,p.Memo,p.Portal,p.Profiler,p.StrictMode,p.Suspense,p.isValidElementType,p.isAsyncMode,p.isConcurrentMode,p.isContextConsumer,p.isContextProvider,p.isElement,p.isForwardRef,p.isFragment,p.isLazy,p.isMemo,p.isPortal,p.isProfiler,p.isStrictMode,p.isSuspense;var y=d((function(e,t){}));f(y);y.typeOf,y.AsyncMode,y.ConcurrentMode,y.ContextConsumer,y.ContextProvider,y.Element,y.ForwardRef,y.Fragment,y.Lazy,y.Memo,y.Portal,y.Profiler,y.StrictMode,y.Suspense,y.isValidElementType,y.isAsyncMode,y.isConcurrentMode,y.isContextConsumer,y.isContextProvider,y.isElement,y.isForwardRef,y.isFragment,y.isLazy,y.isMemo,y.isPortal,y.isProfiler,y.isStrictMode,y.isSuspense,d((function(e){e.exports=p}));var m={initialChunks:{}};var h=function(e){return e};function v(e){var o=e.defaultResolveComponent,i=void 0===o?h:o,s=e.render,f=e.onLoad;function d(e,o){void 0===o&&(o={});var d=function(e){return"function"==typeof e?{requireAsync:e,resolve:function(){},chunkName:function(){}}:e}(e),p={};function y(e){return o.cacheKey?o.cacheKey(e):d.resolve?d.resolve(e):"static"}function h(e,t,n){var a=o.resolveComponent?o.resolveComponent(e,t):i(e);if(o.resolveComponent&&!(void 0)(a))throw new Error("resolveComponent returned something that is not a React component!");return r(n,a,{preload:!0}),a}var v,b,_=function(e){var t=y(e),r=p[t];return r&&"REJECTED"!==r.status||((r=d.requireAsync(e)).status="PENDING",p[t]=r,r.then((function(){r.status="RESOLVED"}),(function(t){console.error("loadable-components: failed to asynchronously load component",{fileName:d.resolve(e),chunkName:d.chunkName(e),error:t?t.message:t}),r.status="REJECTED"}))),r},C=function(e){var t,r;function a(t){var r;return(r=e.call(this,t)||this).state={result:null,error:null,loading:!0,cacheKey:y(t)},n(!t.__chunkExtractor||d.requireSync,"SSR requires `@loadable/babel-plugin`, please install it"),t.__chunkExtractor?!1===o.ssr?l(r):(d.requireAsync(t).catch((function(){return null})),r.loadSync(),t.__chunkExtractor.addChunk(d.chunkName(t)),l(r)):(!1!==o.ssr&&(d.isReady&&d.isReady(t)||d.chunkName&&m.initialChunks[d.chunkName(t)])&&r.loadSync(),r)}r=e,(t=a).prototype=Object.create(r.prototype),t.prototype.constructor=t,t.__proto__=r,a.getDerivedStateFromProps=function(e,t){var r=y(e);return u({},t,{cacheKey:r,loading:t.loading||t.cacheKey!==r})};var i=a.prototype;return i.componentDidMount=function(){this.mounted=!0;var e=this.getCache();e&&"REJECTED"===e.status&&this.setCache(),this.state.loading&&this.loadAsync()},i.componentDidUpdate=function(e,t){t.cacheKey!==this.state.cacheKey&&this.loadAsync()},i.componentWillUnmount=function(){this.mounted=!1},i.safeSetState=function(e,t){this.mounted&&this.setState(e,t)},i.getCacheKey=function(){return y(this.props)},i.getCache=function(){return p[this.getCacheKey()]},i.setCache=function(e){void 0===e&&(e=void 0),p[this.getCacheKey()]=e},i.triggerOnLoad=function(){var e=this;f&&setTimeout((function(){f(e.state.result,e.props)}))},i.loadSync=function(){if(this.state.loading)try{var e=h(d.requireSync(this.props),this.props,E);this.state.result=e,this.state.loading=!1}catch(e){console.error("loadable-components: failed to synchronously load component, which expected to be available",{fileName:d.resolve(this.props),chunkName:d.chunkName(this.props),error:e?e.message:e}),this.state.error=e}},i.loadAsync=function(){var e=this,t=this.resolveAsync();return t.then((function(t){var r=h(t,e.props,E);e.safeSetState({result:r,loading:!1},(function(){return e.triggerOnLoad()}))})).catch((function(t){return e.safeSetState({error:t,loading:!1})})),t},i.resolveAsync=function(){var e=this.props,t=(e.__chunkExtractor,e.forwardedRef,c(e,["__chunkExtractor","forwardedRef"]));return _(t)},i.render=function(){var e=this.props,t=e.forwardedRef,r=e.fallback,n=(e.__chunkExtractor,c(e,["forwardedRef","fallback","__chunkExtractor"])),a=this.state,i=a.error,l=a.loading,f=a.result;if(o.suspense&&"PENDING"===(this.getCache()||this.loadAsync()).status)throw this.loadAsync();if(i)throw i;var d=r||o.fallback||null;return l?d:s({fallback:d,result:f,options:o,props:u({},n,{ref:t})})},a}(t.Component),S=(b=function(e){return t.createElement(a.Consumer,null,(function(r){return t.createElement(v,Object.assign({__chunkExtractor:r},e))}))},(v=C).displayName&&(b.displayName=v.displayName+"WithChunkExtractor"),b),E=t.forwardRef((function(e,r){return t.createElement(S,Object.assign({forwardedRef:r},e))}));return E.displayName="Loadable",E.preload=function(e){E.load(e)},E.load=function(e){return _(e)},E}return{loadable:d,lazy:function(e,t){return d(e,u({},t,{suspense:!0}))}}}var b=v({defaultResolveComponent:function(e){return e.__esModule?e.default:e.default||e},render:function(e){var r=e.result,n=e.props;return t.createElement(r,n)}}),_=b.loadable,C=b.lazy,S=v({onLoad:function(e,t){e&&t.forwardedRef&&("function"==typeof t.forwardedRef?t.forwardedRef(e):t.forwardedRef.current=e)},render:function(e){var t=e.result,r=e.props;return r.children?r.children(t):null}}),E=S.loadable,w=S.lazy,g="undefined"!=typeof window;var R=_;R.lib=E;var x=C;x.lib=w;var O=s;e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=O,e.default=R,e.lazy=x,e.loadableReady=function(e,t){void 0===e&&(e=function(){});var r=void 0===t?{}:t,n=r.namespace,a=void 0===n?"":n,s=r.chunkLoadingGlobal,c=void 0===s?"__LOADABLE_LOADED_CHUNKS__":s;if(!g)return o("`loadableReady()` must be called in browser only"),e(),Promise.resolve();var u=null;if(g){var l=i(a),f=document.getElementById(l);if(f){u=JSON.parse(f.textContent);var d=document.getElementById(l+"_ext");if(!d)throw new Error("loadable-component: @loadable/server does not match @loadable/component");JSON.parse(d.textContent).namedChunks.forEach((function(e){m.initialChunks[e]=!0}))}}if(!u)return o("`loadableReady()` requires state, please use `getScriptTags` or `getScriptElements` server-side"),e(),Promise.resolve();var p=!1;return new Promise((function(e){window[c]=window[c]||[];var t=window[c],r=t.push.bind(t);function n(){u.every((function(e){return t.some((function(t){return t[0].indexOf(e)>-1}))}))&&(p||(p=!0,e()))}t.push=function(){r.apply(void 0,arguments),n()},n()})).then(e)},Object.defineProperty(e,"__esModule",{value:!0})}));
