{"name": "@tanstack/query-core", "version": "4.40.0", "description": "The framework agnostic core that powers TanStack Query", "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/TanStack/query.git", "directory": "packages/query-core"}, "homepage": "https://tanstack.com/query", "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "types": "build/lib/index.d.ts", "main": "build/lib/index.js", "module": "build/lib/index.esm.js", "exports": {".": {"types": "./build/lib/index.d.ts", "import": "./build/lib/index.mjs", "default": "./build/lib/index.js"}, "./package.json": "./package.json"}, "sideEffects": false, "files": ["build/lib/*", "build/umd/*", "src"], "scripts": {}}