{"name": "@exodus/schemasafe", "version": "1.3.0", "description": "JSON Safe Parser & Schema Validator", "license": "MIT", "main": "src/index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/ExodusMovement/schemasafe.git"}, "bugs": {"url": "https://github.com/ExodusMovement/schemasafe/issues"}, "homepage": "https://github.com/ExodusMovement/schemasafe", "files": ["index.d.ts", "src/compile.js", "src/formats.js", "src/generate-function.js", "src/index.js", "src/javascript.js", "src/known-keywords.js", "src/pointer.js", "src/safe-format.js", "src/scope-functions.js", "src/scope-utils.js", "src/tracing.js"], "scripts": {"lint": "prettier --list-different '**/*.js' && eslint .", "format": "prettier --write '**/*.js'", "coverage": "c8 --reporter=lcov --reporter=text npm run test", "coverage:lcov": "c8 --reporter=lcovonly npm run test", "test:only:json-schema-testsuite": "tape test/json-schema.js | tap-spec", "test": "npm run test:raw | tap-spec", "test:raw": "npm run test:normal && npm run test:module", "test:module": "tape -r ./test/tools/test-module.js test/*.js test/regressions/*.js", "test:normal": "tape test/*.js test/regressions/*.js"}, "dependencies": {}, "devDependencies": {"c8": "^7.9.0", "eslint": "^7.32.0", "eslint-config-prettier": "^3.1.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.24.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "prettier": "~1.14.3", "safe-regex": "^1.1.0", "tap-spec": "^5.0.0", "tape": "^5.3.1"}, "resolutions": {"tap-spec/tap-out/trim": "^1.0.1"}, "keywords": ["JSON", "schema", "validator", "validation", "JSON Schema", "draft-04", "draft-06", "draft-07", "draft 2019-09", "draft 2020-12", "jsonschema", "json-schema", "json-schema-validator", "json-schema-validation"]}