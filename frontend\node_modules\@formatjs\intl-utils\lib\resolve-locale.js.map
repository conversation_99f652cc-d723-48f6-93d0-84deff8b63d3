{"version": 3, "file": "resolve-locale.js", "sourceRoot": "", "sources": ["../src/resolve-locale.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAO,EAAC,mBAAmB,EAAC,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAC,SAAS,EAAC,MAAM,aAAa,CAAC;AACtC,OAAO,EAAC,QAAQ,EAAE,SAAS,EAAC,MAAM,kBAAkB,CAAC;AASrD,MAAM,UAAU,mBAAmB,CAGjC,gBAA8B;IAC9B,IAAM,aAAa,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;IAC5D,IAAM,cAAc,GAAG,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;IAC9D;;OAEG;IACH,OAAO,SAAS,aAAa,CAC3B,gBAA0B,EAC1B,gBAA0B,EAC1B,OAAqD,EACrD,qBAA0B,EAC1B,UAA6B;QAE7B,IAAM,OAAO,GAAG,OAAO,CAAC,aAAa,CAAC;QACtC,IAAI,CAAsB,CAAC;QAC3B,IAAI,OAAO,KAAK,QAAQ,EAAE;YACxB,CAAC,GAAG,aAAa,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,CAAC;SACvD;aAAM;YACL,CAAC,GAAG,cAAc,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,CAAC;SACxD;QACD,IAAI,WAAW,GAAG,CAAC,CAAC,MAAM,CAAC;QAC3B,IAAM,MAAM,GAAwB,EAAC,MAAM,EAAE,EAAE,EAAE,UAAU,EAAE,WAAW,EAAC,CAAC;QAC1E,IAAI,kBAAkB,GAAG,IAAI,CAAC;QAC9B,KAAkB,UAAqB,EAArB,+CAAqB,EAArB,mCAAqB,EAArB,IAAqB,EAAE;YAApC,IAAM,GAAG,8BAAA;YACZ,IAAM,eAAe,GAAG,UAAU,CAAC,WAAW,CAAC,CAAC;YAChD,SAAS,CACP,OAAO,eAAe,KAAK,QAAQ,IAAI,eAAe,KAAK,IAAI,EAC/D,iBAAe,GAAG,uBAAoB,CACvC,CAAC;YACF,IAAM,aAAa,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC;YAC3C,SAAS,CACP,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAC5B,uBAAqB,GAAG,sBAAmB,CAC5C,CAAC;YACF,IAAI,KAAK,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;YAC7B,SAAS,CACP,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAC3C,8BAA8B,CAC/B,CAAC;YACF,IAAI,0BAA0B,GAAG,EAAE,CAAC;YACpC,IAAI,CAAC,CAAC,SAAS,EAAE;gBACf,IAAM,cAAc,GAAG,qBAAqB,CAAC,CAAC,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;gBAC/D,IAAI,cAAc,KAAK,SAAS,EAAE;oBAChC,IAAI,cAAc,KAAK,EAAE,EAAE;wBACzB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE;4BAC1C,KAAK,GAAG,cAAc,CAAC;4BACvB,0BAA0B,GAAG,MAAI,GAAG,SAAI,KAAO,CAAC;yBACjD;qBACF;yBAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;wBAC1C,KAAK,GAAG,MAAM,CAAC;wBACf,0BAA0B,GAAG,MAAI,GAAK,CAAC;qBACxC;iBACF;aACF;YACD,IAAI,GAAG,IAAI,OAAO,EAAE;gBAClB,IAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;gBAClC,SAAS,CACP,OAAO,YAAY,KAAK,QAAQ;oBAC9B,OAAO,YAAY,KAAK,WAAW;oBACnC,YAAY,KAAK,IAAI,EACvB,gDAAgD,CACjD,CAAC;gBACF,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;oBACxC,IAAI,YAAY,KAAK,KAAK,EAAE;wBAC1B,KAAK,GAAG,YAAY,CAAC;wBACrB,0BAA0B,GAAG,EAAE,CAAC;qBACjC;iBACF;aACF;YACD,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;YACpB,kBAAkB,IAAI,0BAA0B,CAAC;SAClD;QACD,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;YACjC,IAAM,YAAY,GAAG,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAChD,IAAI,YAAY,KAAK,CAAC,CAAC,EAAE;gBACvB,WAAW,GAAG,WAAW,GAAG,kBAAkB,CAAC;aAChD;iBAAM;gBACL,IAAM,YAAY,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;gBACxD,IAAM,aAAa,GAAG,WAAW,CAAC,KAAK,CACrC,YAAY,EACZ,WAAW,CAAC,MAAM,CACnB,CAAC;gBACF,WAAW,GAAG,YAAY,GAAG,kBAAkB,GAAG,aAAa,CAAC;aACjE;YACD,WAAW,GAAG,mBAAmB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;SACnD;QACD,MAAM,CAAC,MAAM,GAAG,WAAW,CAAC;QAC5B,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACH,SAAS,qBAAqB,CAAC,SAAiB,EAAE,GAAW;IAC3D,SAAS,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE,0BAA0B,CAAC,CAAC;IACxD,IAAM,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC;IAC9B,IAAI,WAAW,GAAG,MAAI,GAAG,MAAG,CAAC;IAC7B,IAAI,GAAG,GAAG,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;IACzC,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;QACd,IAAM,KAAK,GAAG,GAAG,GAAG,CAAC,CAAC;QACtB,IAAI,GAAG,GAAG,KAAK,CAAC;QAChB,IAAI,CAAC,GAAG,KAAK,CAAC;QACd,IAAI,IAAI,GAAG,KAAK,CAAC;QACjB,OAAO,CAAC,IAAI,EAAE;YACZ,IAAM,CAAC,GAAG,SAAS,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACpC,IAAI,GAAG,SAAA,CAAC;YACR,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;gBACZ,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC;aAChB;iBAAM;gBACL,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;aACb;YACD,IAAI,GAAG,KAAK,CAAC,EAAE;gBACb,IAAI,GAAG,IAAI,CAAC;aACb;iBAAM,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;gBACnB,GAAG,GAAG,IAAI,CAAC;gBACX,IAAI,GAAG,IAAI,CAAC;aACb;iBAAM;gBACL,GAAG,GAAG,CAAC,CAAC;gBACR,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;aACX;SACF;QACD,OAAO,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;KACpC;IACD,WAAW,GAAG,MAAI,GAAK,CAAC;IACxB,GAAG,GAAG,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;IACrC,IAAI,GAAG,KAAK,CAAC,CAAC,IAAI,GAAG,GAAG,CAAC,KAAK,IAAI,EAAE;QAClC,OAAO,EAAE,CAAC;KACX;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,IAAM,gCAAgC,GAAG,yBAAyB,CAAC;AAEnE;;;;GAIG;AACH,SAAS,mBAAmB,CAAC,gBAA0B,EAAE,MAAc;IACrE,IAAI,SAAS,GAAG,MAAM,CAAC;IACvB,OAAO,IAAI,EAAE;QACX,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;YACxC,OAAO,SAAS,CAAC;SAClB;QACD,IAAI,GAAG,GAAG,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QACrC,IAAI,CAAC,CAAC,GAAG,EAAE;YACT,OAAO,SAAS,CAAC;SAClB;QACD,IAAI,GAAG,IAAI,CAAC,IAAI,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;YAC1C,GAAG,IAAI,CAAC,CAAC;SACV;QACD,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;KACrC;AACH,CAAC;AAQD,SAAS,mBAAmB,CAAC,gBAA8B;IACzD;;OAEG;IACH,OAAO,SAAS,aAAa,CAC3B,gBAA0B,EAC1B,gBAA0B;QAE1B,IAAM,MAAM,GAAwB,EAAC,MAAM,EAAE,EAAE,EAAC,CAAC;QACjD,KAAqB,UAAgB,EAAhB,qCAAgB,EAAhB,8BAAgB,EAAhB,IAAgB,EAAE;YAAlC,IAAM,MAAM,yBAAA;YACf,IAAM,iBAAiB,GAAG,MAAM,CAAC,OAAO,CACtC,gCAAgC,EAChC,EAAE,CACH,CAAC;YACF,IAAM,eAAe,GAAG,mBAAmB,CACzC,gBAAgB,EAChB,iBAAiB,CAClB,CAAC;YACF,IAAI,eAAe,EAAE;gBACnB,MAAM,CAAC,MAAM,GAAG,eAAe,CAAC;gBAChC,IAAI,MAAM,KAAK,iBAAiB,EAAE;oBAChC,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,KAAK,CAC7B,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAC5B,MAAM,CAAC,MAAM,CACd,CAAC;iBACH;gBACD,OAAO,MAAM,CAAC;aACf;SACF;QACD,MAAM,CAAC,MAAM,GAAG,gBAAgB,EAAE,CAAC;QACnC,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,oBAAoB,CAAC,gBAA8B;IAC1D,OAAO,SAAS,cAAc,CAC5B,gBAA0B,EAC1B,gBAA0B;QAE1B,IAAM,MAAM,GAAwB,EAAC,MAAM,EAAE,EAAE,EAAC,CAAC;QACjD,KAAqB,UAAgB,EAAhB,qCAAgB,EAAhB,8BAAgB,EAAhB,IAAgB,EAAE;YAAlC,IAAM,MAAM,yBAAA;YACf,IAAM,iBAAiB,GAAG,MAAM,CAAC,OAAO,CACtC,gCAAgC,EAChC,EAAE,CACH,CAAC;YACF,IAAM,eAAe,GAAG,mBAAmB,CACzC,gBAAgB,EAChB,iBAAiB,CAClB,CAAC;YACF,IAAI,eAAe,EAAE;gBACnB,MAAM,CAAC,MAAM,GAAG,eAAe,CAAC;gBAChC,IAAI,MAAM,KAAK,iBAAiB,EAAE;oBAChC,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,KAAK,CAC7B,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAC5B,MAAM,CAAC,MAAM,CACd,CAAC;iBACH;gBACD,OAAO,MAAM,CAAC;aACf;SACF;QACD,MAAM,CAAC,MAAM,GAAG,gBAAgB,EAAE,CAAC;QACnC,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,kBAAkB,CAChC,MAAc,EACd,OAA+B,EAC/B,aAAqC;IAErC,IAAM,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC;IACzB,IAAI,OAAO,CAAC,MAAiB,CAAC,EAAE;QAC9B,MAAM,GAAG,OAAO,CAAC,MAAiB,CAAC,CAAC;QACpC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACtB;IACD,IAAM,YAAY,GAAG,aAAa,CAAC,MAAkB,CAAC,CAAC;IACvD,IAAI,YAAY,EAAE;QAChB,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;KAC5B;IACD,IAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACtC,KAAK,IAAI,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QAC3C,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;KACrD;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,sBAAsB,CAC7B,gBAA0B,EAC1B,gBAA0B;IAE1B,IAAM,MAAM,GAAa,EAAE,CAAC;IAC5B,KAAqB,UAAgB,EAAhB,qCAAgB,EAAhB,8BAAgB,EAAhB,IAAgB,EAAE;QAAlC,IAAM,MAAM,yBAAA;QACf,IAAM,iBAAiB,GAAG,MAAM,CAAC,OAAO,CACtC,gCAAgC,EAChC,EAAE,CACH,CAAC;QACF,IAAM,eAAe,GAAG,mBAAmB,CACzC,gBAAgB,EAChB,iBAAiB,CAClB,CAAC;QACF,IAAI,eAAe,EAAE;YACnB,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SAC9B;KACF;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,MAAM,UAAU,gBAAgB,CAC9B,gBAA0B,EAC1B,gBAA0B,EAC1B,OAAiD;IAEjD,IAAI,OAAO,GAA0B,UAAU,CAAC;IAChD,IAAI,OAAO,KAAK,SAAS,EAAE;QACzB,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC;QAC5B,OAAO,GAAG,SAAS,CACjB,OAAO,EACP,eAAe,EACf,QAAQ,EACR,CAAC,QAAQ,EAAE,UAAU,CAAC,EACtB,UAAU,CACG,CAAC;KACjB;IACD,IAAI,OAAO,KAAK,UAAU,EAAE;QAC1B,OAAO,sBAAsB,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,CAAC;KACnE;IACD,OAAO,sBAAsB,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,CAAC;AACpE,CAAC;AAED;IAAqC,0CAAK;IAA1C;QAAA,qEAEC;QADQ,UAAI,GAAG,qBAAqB,CAAC;;IACtC,CAAC;IAAD,6BAAC;AAAD,CAAC,AAFD,CAAqC,KAAK,GAEzC;AAED,MAAM,UAAU,wBAAwB,CACtC,CAAQ;IAER,OAAQ,CAA4B,CAAC,IAAI,KAAK,qBAAqB,CAAC;AACtE,CAAC;AAED,MAAM,UAAU,UAAU,CACxB,MAAc,EACd,UAAyB;AACzB,iDAAiD;AACjD,OAA2D;IAA3D,wBAAA,EAAA,oBAAgC,GAAG,EAAE,CAAC,IAAK,OAAA,uBAAK,GAAG,GAAK,CAAC,EAAE,EAAhB,CAAgB;IAE3D,IAAM,eAAe,GAAG,kBAAkB,CACxC,MAAM,EACN,UAAU,CAAC,OAAO,EAClB,UAAU,CAAC,aAAa,CACzB,CAAC;IACF,IAAM,WAAW,GAAG,eAAe;SAChC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAlB,CAAkB,CAAC;SAC5B,MAAM,CAAC,OAAO,CAAC,CAAC;IACnB,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;QACvB,MAAM,IAAI,sBAAsB,CAC9B,+BAA4B,MAAM,8BAAwB,eAAe,CAAC,IAAI,CAC5E,IAAI,CACH,CACJ,CAAC;KACH;IACD,WAAW,CAAC,OAAO,EAAE,CAAC;IACtB,OAAO,WAAW,CAAC,MAAM,CAAC,OAAO,EAAE,EAAO,CAAC,CAAC;AAC9C,CAAC"}