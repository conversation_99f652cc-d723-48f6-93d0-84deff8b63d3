{"version": 3, "file": "polyfill-utils.js", "sourceRoot": "", "sources": ["../src/polyfill-utils.ts"], "names": [], "mappings": "AAAA,OAAO,OAAO,MAAM,WAAW,CAAC;AAChC,OAAO,aAAa,MAAM,iBAAiB,CAAC;AAC5C,OAAO,EAAC,SAAS,EAAC,MAAM,aAAa,CAAC;AAMtC;;;GAGG;AACH,MAAM,UAAU,QAAQ,CACtB,GAAM;IAEN,IAAI,GAAG,IAAI,IAAI,EAAE;QACf,MAAM,IAAI,SAAS,CAAC,8CAA8C,CAAC,CAAC;KACrE;IACD,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;AACrB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,QAAQ,CAAC,CAAU;IACjC,8BAA8B;IAC9B,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;QACzB,MAAM,SAAS,CAAC,2CAA2C,CAAC,CAAC;KAC9D;IACD,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;AACnB,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,SAAS,CACvB,IAAO,EACP,IAAO,EACP,IAA0B,EAC1B,MAA0B,EAC1B,QAAW;IAEX,kEAAkE;IAClE,IAAI,KAAK,GAAQ,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5B,IAAI,KAAK,KAAK,SAAS,EAAE;QACvB,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,KAAK,QAAQ,EAAE;YAC3C,MAAM,IAAI,SAAS,CAAC,cAAc,CAAC,CAAC;SACrC;QACD,IAAI,IAAI,KAAK,SAAS,EAAE;YACtB,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;SACxB;QACD,IAAI,IAAI,KAAK,QAAQ,EAAE;YACrB,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;SACzB;QACD,IAAI,MAAM,KAAK,SAAS,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,IAAI,KAAK,EAAZ,CAAY,CAAC,CAAC,MAAM,EAAE;YACtE,MAAM,IAAI,UAAU,CAAI,KAAK,uBAAkB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAG,CAAC,CAAC;SACrE;QACD,OAAO,KAAK,CAAC;KACd;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,mBAAmB,CACjC,GAAQ,EACR,GAAW,EACX,GAAW,EACX,QAAgB;IAEhB,IAAI,GAAG,KAAK,SAAS,EAAE;QACrB,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;QAClB,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,EAAE;YACxC,MAAM,IAAI,UAAU,CAAI,GAAG,8BAAyB,GAAG,UAAK,GAAG,MAAG,CAAC,CAAC;SACrE;QACD,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;KACxB;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;;;;;;GAOG;AAEH,MAAM,UAAU,eAAe,CAC7B,OAAU,EACV,QAAW,EACX,OAAe,EACf,OAAe,EACf,QAAgB;IAEhB,IAAM,GAAG,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC9B,OAAO,mBAAmB,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;AAC9D,CAAC;AAED,MAAM,UAAU,gBAAgB,CAAC,IAAY;IAC3C,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,UAAC,GAA2B,EAAE,MAAM;QACrE,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;YACjC,GAAG,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,MAAiB,CAAC,CAAC;SAC1C;QACD,OAAO,GAAG,CAAC;IACb,CAAC,EAAE,EAAE,CAAC,CAAC;AACT,CAAC;AAED,MAAM,UAAU,sBAAsB,CAAC,IAAY;IACjD,OAAO,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,MAAM,CACtC,UAAC,GAA2B,EAAE,MAAM;QAClC,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;YACjC,GAAG,CAAC,MAAM,CAAC,GAAG,aAAa,CAAC,MAAkB,CAAC,CAAC;SACjD;QACD,OAAO,GAAG,CAAC;IACb,CAAC,EACD,EAAE,CACH,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,eAAe,CAK7B,GAAgC,EAChC,EAAY,EACZ,KAAY,EACZ,KAAmC;IAEnC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;QAChB,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;KAClC;IACD,IAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,EAAE,CAAE,CAAC;IAC3B,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;AACvB,CAAC;AAED,MAAM,UAAU,qBAAqB,CAKnC,GAAgC,EAChC,EAAY,EACZ,KAAqC;IAErC,KAAgB,UAAyB,EAAzB,KAAA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAQ,EAAzB,cAAyB,EAAzB,IAAyB,EAAE;QAAtC,IAAM,CAAC,SAAA;QACV,eAAe,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;KACvC;AACH,CAAC;AAED,MAAM,UAAU,eAAe,CAK7B,GAAgC,EAChC,EAAY,EACZ,KAAY;IAEZ,OAAO,qBAAqB,CAAC,GAAG,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;AACtD,CAAC;AAED,MAAM,UAAU,qBAAqB,CAKnC,GAAgC,EAChC,EAAY;IACZ,gBAAkB;SAAlB,UAAkB,EAAlB,qBAAkB,EAAlB,IAAkB;QAAlB,+BAAkB;;IAElB,IAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAC1B,IAAI,CAAC,KAAK,EAAE;QACV,MAAM,IAAI,SAAS,CAAI,EAAE,2CAAwC,CAAC,CAAC;KACpE;IACD,OAAO,MAAM,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,CAAC;QAC1B,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAClB,OAAO,GAAG,CAAC;IACb,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAA0B,CAAC,CAAC;AACnD,CAAC;AAOD,MAAM,UAAU,aAAa,CAC3B,WAAyD;IAEzD,OAAO,WAAW,CAAC,IAAI,KAAK,SAAS,CAAC;AACxC,CAAC;AAED,MAAM,UAAU,gBAAgB,CAAC,OAAe;IAC9C,IAAM,MAAM,GAAG,EAAE,CAAC;IAClB,IAAI,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IACtC,IAAI,QAAQ,GAAG,CAAC,CAAC;IACjB,IAAI,SAAS,GAAG,CAAC,CAAC;IAClB,IAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAC9B,OAAO,UAAU,GAAG,OAAO,CAAC,MAAM,IAAI,UAAU,GAAG,CAAC,CAAC,EAAE;QACrD,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;QAC5C,SAAS,CAAC,QAAQ,GAAG,UAAU,EAAE,qBAAmB,OAAS,CAAC,CAAC;QAC/D,IAAI,UAAU,GAAG,SAAS,EAAE;YAC1B,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,SAAS;gBACf,KAAK,EAAE,OAAO,CAAC,SAAS,CAAC,SAAS,EAAE,UAAU,CAAC;aAChD,CAAC,CAAC;SACJ;QACD,MAAM,CAAC,IAAI,CAAC;YACV,IAAI,EAAE,OAAO,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,EAAE,QAAQ,CAAC;YACjD,KAAK,EAAE,SAAS;SACjB,CAAC,CAAC;QACH,SAAS,GAAG,QAAQ,GAAG,CAAC,CAAC;QACzB,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;KAC9C;IACD,IAAI,SAAS,GAAG,MAAM,EAAE;QACtB,MAAM,CAAC,IAAI,CAAC;YACV,IAAI,EAAE,SAAS;YACf,KAAK,EAAE,OAAO,CAAC,SAAS,CAAC,SAAS,EAAE,MAAM,CAAC;SAC5C,CAAC,CAAC;KACJ;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,2BAA2B,CAIzC,eAAiD,EACjD,OAAgB,EAChB,IAA8B,EAC9B,WAAmB,EACnB,WAAmB;IAEnB,IAAM,IAAI,GAAG,eAAe,CAAC,IAAI,EAAE,sBAAsB,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IACrE,IAAI,IAAI,GAAG,IAAI,CAAC,qBAAqB,CAAC;IACtC,IAAI,IAAI,GAAG,IAAI,CAAC,qBAAqB,CAAC;IACtC,IAAI,IAAI,GAAG,IAAI,CAAC,wBAAwB,CAAC;IACzC,IAAI,IAAI,GAAG,IAAI,CAAC,wBAAwB,CAAC;IACzC,eAAe,CAAC,eAAe,EAAE,OAAO,EAAE,sBAAsB,EAAE,IAAI,CAAC,CAAC;IACxE,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,KAAK,SAAS,EAAE;QAC5C,eAAe,CACb,eAAe,EACf,OAAO,EACP,cAAc,EACd,mBAAmB,CACpB,CAAC;QACF,IAAI,GAAG,mBAAmB,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;QAC3C,IAAI,GAAG,mBAAmB,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;QAC/C,eAAe,CAAC,eAAe,EAAE,OAAO,EAAE,0BAA0B,EAAE,IAAI,CAAC,CAAC;QAC5E,eAAe,CAAC,eAAe,EAAE,OAAO,EAAE,0BAA0B,EAAE,IAAI,CAAC,CAAC;KAC7E;SAAM,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,KAAK,SAAS,EAAE;QACnD,eAAe,CAAC,eAAe,EAAE,OAAO,EAAE,cAAc,EAAE,gBAAgB,CAAC,CAAC;QAC5E,IAAI,GAAG,mBAAmB,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,WAAW,CAAC,CAAC;QACrD,IAAM,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QACtD,IAAI,GAAG,mBAAmB,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,iBAAiB,CAAC,CAAC;QAC9D,eAAe,CAAC,eAAe,EAAE,OAAO,EAAE,uBAAuB,EAAE,IAAI,CAAC,CAAC;QACzE,eAAe,CAAC,eAAe,EAAE,OAAO,EAAE,uBAAuB,EAAE,IAAI,CAAC,CAAC;KAC1E;SAAM,IACL,eAAe,CAAC,eAAe,EAAE,OAAO,EAAE,UAAU,CAAC,KAAK,SAAS,EACnE;QACA,eAAe,CACb,eAAe,EACf,OAAO,EACP,cAAc,EACd,iBAAiB,CAClB,CAAC;KACH;SAAM;QACL,eAAe,CAAC,eAAe,EAAE,OAAO,EAAE,cAAc,EAAE,gBAAgB,CAAC,CAAC;QAC5E,eAAe,CACb,eAAe,EACf,OAAO,EACP,uBAAuB,EACvB,WAAW,CACZ,CAAC;QACF,eAAe,CACb,eAAe,EACf,OAAO,EACP,uBAAuB,EACvB,WAAW,CACZ,CAAC;KACH;AACH,CAAC;AAED,MAAM,UAAU,QAAQ,CAAC,CAAM,EAAE,CAAM;IACrC,IAAI,MAAM,CAAC,EAAE,EAAE;QACb,OAAO,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KACxB;IACD,sBAAsB;IACtB,IAAI,CAAC,KAAK,CAAC,EAAE;QACX,kBAAkB;QAClB,0BAA0B;QAC1B,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;KACnC;IACD,uBAAuB;IACvB,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5B,CAAC;AAED,IAAM,aAAa,GAAG,QAAQ,CAAC;AAE/B;;;GAGG;AACH,SAAS,WAAW,CAAC,GAAW;IAC9B,OAAO,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,WAAW,EAAE,EAAf,CAAe,CAAC,CAAC;AAC5D,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,wBAAwB,CAAC,QAAgB;IACvD,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;IACjC,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;QACzB,OAAO,KAAK,CAAC;KACd;IACD,IAAI,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;QAChC,OAAO,KAAK,CAAC;KACd;IACD,OAAO,IAAI,CAAC;AACd,CAAC"}