{"mappings": "OAEA,MAAMA,aAAe,CAAC,MAAO,QAAS,SAAU,eAChD,MAAMC,cAAgB,CAAC,QAAS,SAAU,cAmD1C,SAASC,kBAAiBC,WACxBA,EADwBC,WAExBA,EAFwBC,UAGxBA,EAHwBC,YAIxBA,EAAc,EAJUC,KAKxBA,EALwBC,WAMxBA,EAAa,EANWC,MAOxBA,EAPwBC,YAQxBA,EAAc,EARUC,sBASxBA,GAAwB,EATAC,wBAUxBA,EAVwBC,mBAWxBA,EAAqB,IAIrB,IAAKV,IAAeC,IAAeQ,EACjC,MAAO,CACLE,aAAcC,EACdC,YAAaC,GAKjB,MAAMC,EA+GR,SACEd,EACAD,EACAK,EAAqB,EACrBE,EAAsB,EACtBL,GAEA,MAAMc,EAAuBd,EAAYA,EAAUe,OAAS,EAEtDC,EAAIC,EAAsBnB,EAAYC,EAAY,KAClDmB,EAAID,EAAsBnB,EAAYC,EAAY,KAElDoB,EAAUD,EAAEE,OAASjB,EAAaW,EAClCO,EAAUH,EAAEI,MAASnB,EAAaW,EAClCS,EAAUP,EAAEI,OAASjB,EAAaW,EAClCU,EAAUR,EAAEM,MAASnB,EAAaW,EA0BxC,MAvBgC,CAC9BW,IAAK,CACHC,MAAQ,CAAEV,EAAGA,EAAEU,MAAQrB,EAAaa,EAAGC,GACvCQ,OAAQ,CAAEX,EAAGA,EAAEW,OAAqBT,EAAGC,GACvCS,IAAQ,CAAEZ,EAAGA,EAAEY,IAAMvB,EAAea,EAAGC,IAEzCU,MAAO,CACLH,MAAQ,CAAEV,EAAGQ,EAAQN,EAAGA,EAAEQ,MAAQrB,GAClCsB,OAAQ,CAAEX,EAAGQ,EAAQN,EAAGA,EAAES,QAC1BC,IAAQ,CAAEZ,EAAGQ,EAAQN,EAAGA,EAAEU,IAAMvB,IAElCyB,OAAQ,CACNJ,MAAQ,CAAEV,EAAGA,EAAEU,MAAQrB,EAAaa,EAAGG,GACvCM,OAAQ,CAAEX,EAAGA,EAAEW,OAAqBT,EAAGG,GACvCO,IAAQ,CAAEZ,EAAGA,EAAEY,IAAMvB,EAAea,EAAGG,IAEzCU,KAAM,CACJL,MAAQ,CAAEV,EAAGO,EAAOL,EAAGA,EAAEQ,MAAQrB,GACjCsB,OAAQ,CAAEX,EAAGO,EAAOL,EAAGA,EAAES,QACzBC,IAAQ,CAAEZ,EAAGO,EAAOL,EAAGA,EAAEU,IAAMvB,KApJR2B,CACzBjC,EACAD,EACAK,EACAE,EACAL,GAIIiC,EAAcpB,EAAmBX,GAAME,GAG7C,IAA8B,IAA1BE,EAAiC,CACnC,MAAMG,EAAeyB,EAA2BD,GAEhD,IAAItB,EAAcC,EACdZ,IACFW,EAAcwB,EAAqB,CAAEpC,WAAAA,EAAYC,UAAAA,EAAWC,YAAAA,EAAaC,KAAAA,EAAME,MAAAA,KAKjF,MAAO,CACLK,aAAc,IACTA,EACH2B,kCALoBC,EAAmBtC,EAAYG,EAAME,EAAOH,EAAaD,IAO/EW,YAAAA,EACA2B,WAAYpC,EACZqC,YAAanC,GAKjB,MAAMoC,EAAaC,QAAQC,SAAS,IAAK3C,KAAekC,IAGlDU,GAiXmBC,EAhXvBrC,EAgXyCsC,EA/WzCrC,EAgXKiC,QAAQC,SAAS,CACtBI,MAAOF,EAAKE,MAAiB,EAATD,EACpB9B,OAAQ6B,EAAK7B,OAAkB,EAAT8B,EACtB7B,EAAG4B,EAAKb,KAAOc,EACf3B,EAAG0B,EAAKnB,IAAMoB,KALlB,IAA2BD,EAAkBC,EA3W3C,MAAME,EAAmBC,EAAcR,EAAYG,GAK7CM,EAA0BpC,EADXqC,EAAgBhD,IAC4BE,GAW3DkC,EAqHR,SAEEpC,EAEAiD,EAEAC,GAEA,MAAMC,EAAeH,EAAgBhD,GAGrC,OAAOiD,EAAWjD,KAAUkD,EAAuBC,GAAgBA,EAAenD,EAhI/DoD,CACjBpD,EACA6C,EARmCC,EAJEP,QAAQC,SAAS,IACnD3C,KACAkD,IAIHN,IAWIJ,EA+HR,SAEExC,EAEAwD,EAEArD,EAEAE,EAEA+C,GAEA,MAAMK,EAA4B,QAATtD,GAA2B,WAATA,EACrCuD,EAAaD,EAAmB,OAAS,MACzCE,EAAWF,EAAmB,QAAU,SACxCG,EAAYH,EAAmB,QAAU,SACzCI,EAAiBL,EAAWI,GAAa5D,EAAW4D,GAE1D,IAAc,UAAVvD,GAA+B,WAAVA,KAClB+C,EAAWM,IAAeG,GAAoBT,EAAWO,KAAcE,GAC1E,MAAO,MAIX,IAAc,QAAVxD,GAA6B,WAAVA,KAChB+C,EAAWO,IAAaE,GAAoBT,EAAWM,KAAgBG,GAC1E,MAAO,QAIX,OAAOxD,EA7JayD,CAClB9D,EACAD,EACAI,EACAE,EACA2C,GAMItC,EAAeyB,EAHKrB,EAAmByB,GAAYC,IAKzD,IAAI5B,EAAcC,EACdZ,IACFW,EAAcwB,EAAqB,CACjCpC,WAAAA,EACAC,UAAAA,EACAC,YAAAA,EACAC,KAAMoC,EACNlC,MAAOmC,KAYX,MAAO,CACL9B,aAAc,IACTA,EACH2B,kCAXoBC,EACtBtC,EACAuC,EACAC,EACAtC,EACAD,IAQAW,YAAAA,EACA2B,WAAAA,EACAC,YAAAA,GAkDJ,SAAStB,EAAsBnB,EAAwBC,EAAkB+D,GACvE,MACMC,EAAcjE,EADO,MAATgE,EAAe,OAAS,OAGpCH,EAAqB,MAATG,EAAe,QAAU,SACrCE,EAAkBlE,EAAW6D,GAC7BM,EAAkBlE,EAAW4D,GAGnC,MAAO,CACLvC,OAAQ2C,EAAcE,EACtBvC,MAAQqC,EACRpC,OAAQoC,GAAeC,EAAkBC,GAAmB,EAC5DrC,IAAQmC,EAAcC,EAAkBC,EACxC3C,MAAQyC,EAAcC,GAyD1B,SAAS9B,EAA2BgC,GAGlC,MAAO,CACLC,SAAU,WACV1C,IAAK,EACLM,KAAM,EACNqC,SAAU,cACVC,WAAY,YACZC,UAAY,eARJC,KAAKC,MAAMN,EAAMlD,EAAIyD,OAAOC,eAC5BH,KAAKC,MAAMN,EAAMhD,EAAIuD,OAAOE,kBAWxC,SAAStC,EACPtC,EACAG,EACAE,EACAH,EACAD,GAEA,MAAMwD,EAA4B,QAATtD,GAA2B,WAATA,EAErC0E,EAAkB5E,EAAYA,EAAU8C,MAAQ,EAEhD3C,EADuBH,EAAYA,EAAUe,OAAS,EAEtDV,EAAcuE,EAAkB,EAAI3E,EAE1C,IAAIe,EAAI,GACJE,EAAI,GAoBR,OAlBIsC,GACFxC,EAAI,CACFU,MAAQ,GAAErB,MACVsB,OAAQ,SACRC,IAAQ7B,EAAW+C,MAAQzC,EAArB,MACND,GAEFc,EAAa,QAAThB,EAAkB,GAAEH,EAAWgB,OAASZ,OAAqBA,EAAH,OAE9Da,EAAa,SAATd,EAAmB,GAAEH,EAAW+C,MAAQ3C,OAAqBA,EAAH,KAE9De,EAAI,CACFQ,MAAQ,GAAErB,MACVsB,OAAQ,SACRC,IAAQ7B,EAAWgB,OAASV,EAAtB,MACND,IAGI,GAAEY,KAAKE,IAGjB,MAAMR,EAA2C,CAG/CyD,SAAU,QACV1C,IAAK,EACLM,KAAM,EACN8C,QAAS,EACTP,UAAW,4BAGP1D,EAA0C,CAG9CuD,SAAU,WACVU,QAAS,GAoBX,SAAS1C,GAAqBpC,WAC5BA,EAD4BC,UAE5BA,EAF4BC,YAG5BA,EAH4BC,KAI5BA,EAJ4BE,MAK5BA,IAEA,MAAM0E,GAAiB/E,EAAW+C,MAAQ9C,EAAU8C,OAAS,EACvDiC,GAAiBhF,EAAWgB,OAASf,EAAU8C,OAAS,EAGxDkC,EADc,CAAEvD,IAAK,EAAGI,MAAO,GAAIC,OAAQ,IAAKC,MAAO,IAChC7B,GACvB+E,EAAoBV,KAAKW,IAAIlF,EAAU8C,MAAO9C,EAAUe,QAExDoE,EAAyB,CAI7BrC,MAAQ,GAAEmC,MACVlE,OAAS,GAAEkE,MAGXX,UAAY,UAASU,QACrBX,WAAY,YAGZF,SAAU,WACViB,CAAClF,GAAO,OAMRmF,UAAWC,EAAqBpF,EAAME,IA2BxC,MAxBa,QAATF,GAA2B,WAATA,IACN,UAAVE,IACF+E,EAAOpD,KAAQ,GAAE9B,OAEL,WAAVG,IACF+E,EAAOpD,KAAQ,GAAE+C,OAEL,QAAV1E,IACF+E,EAAOtD,MAAS,GAAE5B,QAIT,SAATC,GAA4B,UAATA,IACP,UAAVE,IACF+E,EAAO1D,IAAO,GAAExB,OAEJ,WAAVG,IACF+E,EAAO1D,IAAO,GAAEsD,OAEJ,QAAV3E,IACF+E,EAAOrD,OAAU,GAAE7B,QAIhBkF,EAMT,SAASG,EAAqBpF,EAAYE,GACxC,OAAc,QAATF,GAA2B,UAATA,GAA+B,QAAVE,KAI9B,WAATF,GAA8B,SAATA,GAA8B,QAAVE,GAIvC,MAPE,MAaX,SAAS8C,EAAgBhD,GAOvB,MAN0C,CACxCuB,IAAK,SACLI,MAAO,OACPC,OAAQ,MACRC,KAAM,SAEa7B,GAmBvB,SAAS8C,EAEPJ,EAEArC,GAEA,MAAO,CACLkB,IAAKmB,EAAKnB,IAAMlB,EAAwBkB,IACxCI,MAAOe,EAAKf,MAAQtB,EAAwBsB,MAC5CC,OAAQc,EAAKd,OAASvB,EAAwBuB,OAC9CC,KAAMa,EAAKb,KAAOxB,EAAwBwB", "sources": ["./packages/core/popper/src/popper.ts"], "sourcesContent": ["import * as CSS from 'csstype';\n\nconst SIDE_OPTIONS = ['top', 'right', 'bottom', 'left'] as const;\nconst ALIGN_OPTIONS = ['start', 'center', 'end'] as const;\n\ntype Axis = 'x' | 'y';\ntype Side = typeof SIDE_OPTIONS[number];\ntype Align = typeof ALIGN_OPTIONS[number];\ntype Point = { x: number; y: number };\ntype Size = { width: number; height: number };\n\ntype GetPlacementDataOptions = {\n  /** The rect of the anchor we are placing around */\n  anchorRect?: ClientRect;\n  /** The size of the popper to place */\n  popperSize?: Size;\n  /** An optional arrow size */\n  arrowSize?: Size;\n  /** An optional arrow offset (along the side, default: 0) */\n  arrowOffset?: number;\n  /** The desired side */\n  side: Side;\n  /** An optional side offset (distance from the side, default: 0)  */\n  sideOffset?: number;\n  /** The desired alignment */\n  align: Align;\n  /** An optional alignment offset (distance along the side, default: 0) */\n  alignOffset?: number;\n  /** An option to turn on/off the collision handling (default: true) */\n  shouldAvoidCollisions?: boolean;\n  /** The rect which represents the boundaries for collision checks */\n  collisionBoundariesRect?: ClientRect;\n  /** The tolerance used for collisions, ie. if we want them to trigger a bit earlier (default: 0) */\n  collisionTolerance?: number;\n};\n\ntype PlacementData = {\n  popperStyles: CSS.Properties;\n  arrowStyles: CSS.Properties;\n  placedSide?: Side;\n  placedAlign?: Align;\n};\n\n/**\n * Given all the information necessary to compute it,\n * this function calculates all the necessary placement data.\n *\n * It will return:\n *\n * - the styles to apply to the popper (including a custom property that is useful to set the transform origin in the right place)\n * - the styles to apply to the arrow\n * - the placed side (because it might have changed because of collisions)\n * - the placed align (because it might have changed because of collisions)\n */\nfunction getPlacementData({\n  anchorRect,\n  popperSize,\n  arrowSize,\n  arrowOffset = 0,\n  side,\n  sideOffset = 0,\n  align,\n  alignOffset = 0,\n  shouldAvoidCollisions = true,\n  collisionBoundariesRect,\n  collisionTolerance = 0,\n}: GetPlacementDataOptions): PlacementData {\n  // if we're not ready to do all the measurements yet,\n  // we return some good default styles\n  if (!anchorRect || !popperSize || !collisionBoundariesRect) {\n    return {\n      popperStyles: UNMEASURED_POPPER_STYLES,\n      arrowStyles: UNMEASURED_ARROW_STYLES,\n    };\n  }\n\n  // pre-compute points for all potential placements\n  const allPlacementPoints = getAllPlacementPoints(\n    popperSize,\n    anchorRect,\n    sideOffset,\n    alignOffset,\n    arrowSize\n  );\n\n  // get point based on side / align\n  const popperPoint = allPlacementPoints[side][align];\n\n  // if we don't need to avoid collisions, we can stop here\n  if (shouldAvoidCollisions === false) {\n    const popperStyles = getPlacementStylesForPoint(popperPoint);\n\n    let arrowStyles = UNMEASURED_ARROW_STYLES;\n    if (arrowSize) {\n      arrowStyles = getPopperArrowStyles({ popperSize, arrowSize, arrowOffset, side, align });\n    }\n\n    const transformOrigin = getTransformOrigin(popperSize, side, align, arrowOffset, arrowSize);\n\n    return {\n      popperStyles: {\n        ...popperStyles,\n        ['--radix-popper-transform-origin' as any]: transformOrigin,\n      },\n      arrowStyles,\n      placedSide: side,\n      placedAlign: align,\n    };\n  }\n\n  // create a new rect as if element had been moved to new placement\n  const popperRect = DOMRect.fromRect({ ...popperSize, ...popperPoint });\n\n  // create a new rect representing the collision boundaries but taking into account any added tolerance\n  const collisionBoundariesRectWithTolerance = getContractedRect(\n    collisionBoundariesRect,\n    collisionTolerance\n  );\n\n  // check for any collisions in new placement\n  const popperCollisions = getCollisions(popperRect, collisionBoundariesRectWithTolerance);\n\n  // do all the same calculations for the opposite side\n  // this is because we need to check for potential collisions if we were to swap side\n  const oppositeSide = getOppositeSide(side);\n  const oppositeSidePopperPoint = allPlacementPoints[oppositeSide][align];\n  const updatedOppositeSidePopperPoint = DOMRect.fromRect({\n    ...popperSize,\n    ...oppositeSidePopperPoint,\n  });\n  const oppositeSidePopperCollisions = getCollisions(\n    updatedOppositeSidePopperPoint,\n    collisionBoundariesRectWithTolerance\n  );\n\n  // adjust side accounting for collisions / opposite side collisions\n  const placedSide = getSideAccountingForCollisions(\n    side,\n    popperCollisions,\n    oppositeSidePopperCollisions\n  );\n\n  // adjust alignnment accounting for collisions\n  const placedAlign = getAlignAccountingForCollisions(\n    popperSize,\n    anchorRect,\n    side,\n    align,\n    popperCollisions\n  );\n\n  const placedPopperPoint = allPlacementPoints[placedSide][placedAlign];\n\n  // compute adjusted popper / arrow styles\n  const popperStyles = getPlacementStylesForPoint(placedPopperPoint);\n\n  let arrowStyles = UNMEASURED_ARROW_STYLES;\n  if (arrowSize) {\n    arrowStyles = getPopperArrowStyles({\n      popperSize,\n      arrowSize,\n      arrowOffset,\n      side: placedSide,\n      align: placedAlign,\n    });\n  }\n\n  const transformOrigin = getTransformOrigin(\n    popperSize,\n    placedSide,\n    placedAlign,\n    arrowOffset,\n    arrowSize\n  );\n\n  return {\n    popperStyles: {\n      ...popperStyles,\n      ['--radix-popper-transform-origin' as any]: transformOrigin,\n    },\n    arrowStyles,\n    placedSide,\n    placedAlign,\n  };\n}\n\ntype AllPlacementPoints = Record<Side, Record<Align, Point>>;\n\nfunction getAllPlacementPoints(\n  popperSize: Size,\n  anchorRect: ClientRect,\n  sideOffset: number = 0,\n  alignOffset: number = 0,\n  arrowSize?: Size\n): AllPlacementPoints {\n  const arrowBaseToTipLength = arrowSize ? arrowSize.height : 0;\n\n  const x = getPopperSlotsForAxis(anchorRect, popperSize, 'x');\n  const y = getPopperSlotsForAxis(anchorRect, popperSize, 'y');\n\n  const topY    = y.before - sideOffset - arrowBaseToTipLength; // prettier-ignore\n  const bottomY = y.after  + sideOffset + arrowBaseToTipLength; // prettier-ignore\n  const leftX   = x.before - sideOffset - arrowBaseToTipLength; // prettier-ignore\n  const rightX  = x.after  + sideOffset + arrowBaseToTipLength; // prettier-ignore\n\n  // prettier-ignore\n  const map: AllPlacementPoints = {\n    top: {\n      start:  { x: x.start + alignOffset, y: topY },\n      center: { x: x.center,              y: topY },\n      end:    { x: x.end - alignOffset,   y: topY },\n    },\n    right: {\n      start:  { x: rightX, y: y.start + alignOffset },\n      center: { x: rightX, y: y.center },\n      end:    { x: rightX, y: y.end - alignOffset },\n    },\n    bottom: {\n      start:  { x: x.start + alignOffset, y: bottomY },\n      center: { x: x.center,              y: bottomY },\n      end:    { x: x.end - alignOffset,   y: bottomY },\n    },\n    left: {\n      start:  { x: leftX, y: y.start + alignOffset },\n      center: { x: leftX, y: y.center },\n      end:    { x: leftX, y: y.end - alignOffset },\n    },\n  };\n\n  return map;\n}\n\nfunction getPopperSlotsForAxis(anchorRect: ClientRect, popperSize: Size, axis: Axis) {\n  const startSide = axis === 'x' ? 'left' : 'top';\n  const anchorStart = anchorRect[startSide];\n\n  const dimension = axis === 'x' ? 'width' : 'height';\n  const anchorDimension = anchorRect[dimension];\n  const popperDimension = popperSize[dimension];\n\n  // prettier-ignore\n  return {\n    before: anchorStart - popperDimension,\n    start:  anchorStart,\n    center: anchorStart + (anchorDimension - popperDimension) / 2,\n    end:    anchorStart + anchorDimension - popperDimension,\n    after:  anchorStart + anchorDimension,\n  };\n}\n\n/**\n * Gets an adjusted side based on collision information\n */\nfunction getSideAccountingForCollisions(\n  /** The side we want to ideally position to */\n  side: Side,\n  /** The collisions for this given side */\n  collisions: Collisions,\n  /** The collisions for the opposite side (if we were to swap side) */\n  oppositeSideCollisions: Collisions\n): Side {\n  const oppositeSide = getOppositeSide(side);\n  // in order to prevent premature jumps\n  // we only swap side if there's enough space to fit on the opposite side\n  return collisions[side] && !oppositeSideCollisions[oppositeSide] ? oppositeSide : side;\n}\n\n/**\n * Gets an adjusted alignment based on collision information\n */\nfunction getAlignAccountingForCollisions(\n  /** The size of the popper to place */\n  popperSize: Size,\n  /** The size of the anchor we are placing around */\n  anchorSize: Size,\n  /** The final side */\n  side: Side,\n  /** The desired align */\n  align: Align,\n  /** The collisions */\n  collisions: Collisions\n): Align {\n  const isHorizontalSide = side === 'top' || side === 'bottom';\n  const startBound = isHorizontalSide ? 'left' : 'top';\n  const endBound = isHorizontalSide ? 'right' : 'bottom';\n  const dimension = isHorizontalSide ? 'width' : 'height';\n  const isAnchorBigger = anchorSize[dimension] > popperSize[dimension];\n\n  if (align === 'start' || align === 'center') {\n    if ((collisions[startBound] && isAnchorBigger) || (collisions[endBound] && !isAnchorBigger)) {\n      return 'end';\n    }\n  }\n\n  if (align === 'end' || align === 'center') {\n    if ((collisions[endBound] && isAnchorBigger) || (collisions[startBound] && !isAnchorBigger)) {\n      return 'start';\n    }\n  }\n\n  return align;\n}\n\nfunction getPlacementStylesForPoint(point: Point): CSS.Properties {\n  const x = Math.round(point.x + window.scrollX);\n  const y = Math.round(point.y + window.scrollY);\n  return {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    minWidth: 'max-content',\n    willChange: 'transform',\n    transform: `translate3d(${x}px, ${y}px, 0)`,\n  };\n}\n\nfunction getTransformOrigin(\n  popperSize: Size,\n  side: Side,\n  align: Align,\n  arrowOffset: number,\n  arrowSize?: Size\n): CSS.Properties['transformOrigin'] {\n  const isHorizontalSide = side === 'top' || side === 'bottom';\n\n  const arrowBaseLength = arrowSize ? arrowSize.width : 0;\n  const arrowBaseToTipLength = arrowSize ? arrowSize.height : 0;\n  const sideOffset = arrowBaseToTipLength;\n  const alignOffset = arrowBaseLength / 2 + arrowOffset;\n\n  let x = '';\n  let y = '';\n\n  if (isHorizontalSide) {\n    x = {\n      start: `${alignOffset}px`,\n      center: 'center',\n      end: `${popperSize.width - alignOffset}px`,\n    }[align];\n\n    y = side === 'top' ? `${popperSize.height + sideOffset}px` : `${-sideOffset}px`;\n  } else {\n    x = side === 'left' ? `${popperSize.width + sideOffset}px` : `${-sideOffset}px`;\n\n    y = {\n      start: `${alignOffset}px`,\n      center: 'center',\n      end: `${popperSize.height - alignOffset}px`,\n    }[align];\n  }\n\n  return `${x} ${y}`;\n}\n\nconst UNMEASURED_POPPER_STYLES: CSS.Properties = {\n  // position: 'fixed' here is important because it will take the popper\n  // out of the flow so it does not disturb the position of the anchor\n  position: 'fixed',\n  top: 0,\n  left: 0,\n  opacity: 0,\n  transform: 'translate3d(0, -200%, 0)',\n};\n\nconst UNMEASURED_ARROW_STYLES: CSS.Properties = {\n  // given the arrow is nested inside the popper,\n  // make sure that it is out of the flow and doesn't hinder then popper's measurement\n  position: 'absolute',\n  opacity: 0,\n};\n\ntype GetArrowStylesOptions = {\n  /** The size of the popper to place */\n  popperSize: Size;\n  /** The size of the arrow itself */\n  arrowSize: Size;\n  /** An offset for the arrow along the align axis */\n  arrowOffset: number;\n  /** The side where the arrow points to */\n  side: Side;\n  /** The alignment of the arrow along the side */\n  align: Align;\n};\n\n/**\n * Computes the styles necessary to position, rotate and align the arrow correctly.\n * It can adjust itself based on anchor/popper size, side/align and an optional offset.\n */\nfunction getPopperArrowStyles({\n  popperSize,\n  arrowSize,\n  arrowOffset,\n  side,\n  align,\n}: GetArrowStylesOptions): CSS.Properties {\n  const popperCenterX = (popperSize.width - arrowSize.width) / 2;\n  const popperCenterY = (popperSize.height - arrowSize.width) / 2;\n\n  const rotationMap = { top: 0, right: 90, bottom: 180, left: -90 };\n  const rotation = rotationMap[side];\n  const arrowMaxDimension = Math.max(arrowSize.width, arrowSize.height);\n\n  const styles: CSS.Properties = {\n    // we make sure we put the arrow inside a 1:1 ratio container\n    // this is to make the rotation handling simpler\n    // as we do no need to worry about changing the transform-origin\n    width: `${arrowMaxDimension}px`,\n    height: `${arrowMaxDimension}px`,\n\n    // rotate the arrow appropriately\n    transform: `rotate(${rotation}deg)`,\n    willChange: 'transform',\n\n    // position the arrow appropriately\n    position: 'absolute',\n    [side]: '100%',\n\n    // Because the arrow gets rotated (see `transform above`)\n    // and we are putting it inside a 1:1 ratio container\n    // we need to adjust the CSS direction from `ltr` to `rtl`\n    // in some circumstances\n    direction: getArrowCssDirection(side, align),\n  };\n\n  if (side === 'top' || side === 'bottom') {\n    if (align === 'start') {\n      styles.left = `${arrowOffset}px`;\n    }\n    if (align === 'center') {\n      styles.left = `${popperCenterX}px`;\n    }\n    if (align === 'end') {\n      styles.right = `${arrowOffset}px`;\n    }\n  }\n\n  if (side === 'left' || side === 'right') {\n    if (align === 'start') {\n      styles.top = `${arrowOffset}px`;\n    }\n    if (align === 'center') {\n      styles.top = `${popperCenterY}px`;\n    }\n    if (align === 'end') {\n      styles.bottom = `${arrowOffset}px`;\n    }\n  }\n\n  return styles;\n}\n\n/**\n * Adjusts the arrow's CSS direction (`ltr` / `rtl`)\n */\nfunction getArrowCssDirection(side: Side, align: Align): CSS.Property.Direction {\n  if ((side === 'top' || side === 'right') && align === 'end') {\n    return 'rtl';\n  }\n\n  if ((side === 'bottom' || side === 'left') && align !== 'end') {\n    return 'rtl';\n  }\n\n  return 'ltr';\n}\n\n/**\n * Gets the opposite side of a given side (ie. top => bottom, left => right, …)\n */\nfunction getOppositeSide(side: Side): Side {\n  const oppositeSides: Record<Side, Side> = {\n    top: 'bottom',\n    right: 'left',\n    bottom: 'top',\n    left: 'right',\n  };\n  return oppositeSides[side];\n}\n\n/**\n * Creates a new rect (`ClientRect`) based on a given one but contracted by\n * a given amout on each side.\n */\nfunction getContractedRect(rect: ClientRect, amount: number) {\n  return DOMRect.fromRect({\n    width: rect.width - amount * 2,\n    height: rect.height - amount * 2,\n    x: rect.left + amount,\n    y: rect.top + amount,\n  });\n}\n\n/**\n * Gets collisions for each side of a rect (top, right, bottom, left)\n */\nfunction getCollisions(\n  /** The rect to test collisions against */\n  rect: ClientRect,\n  /** The rect which represents the boundaries for collision checks */\n  collisionBoundariesRect: ClientRect\n) {\n  return {\n    top: rect.top < collisionBoundariesRect.top,\n    right: rect.right > collisionBoundariesRect.right,\n    bottom: rect.bottom > collisionBoundariesRect.bottom,\n    left: rect.left < collisionBoundariesRect.left,\n  };\n}\n\ntype Collisions = ReturnType<typeof getCollisions>;\n\nexport { getPlacementData, SIDE_OPTIONS, ALIGN_OPTIONS };\nexport type { Side, Align };\n"], "names": ["SIDE_OPTIONS", "ALIGN_OPTIONS", "getPlacementData", "anchorRect", "popperSize", "arrowSize", "arrowOffset", "side", "sideOffset", "align", "alignOffset", "shouldAvoidCollisions", "collisionBoundariesRect", "collisionTolerance", "popperStyles", "UNMEASURED_POPPER_STYLES", "arrowStyles", "UNMEASURED_ARROW_STYLES", "allPlacementPoints", "arrowBaseToTipLength", "height", "x", "getPopperSlotsForAxis", "y", "topY", "before", "bottomY", "after", "leftX", "rightX", "top", "start", "center", "end", "right", "bottom", "left", "getAllPlacementPoints", "popperPoint", "getPlacementStylesForPoint", "getPopperArrowStyles", "--radix-popper-transform-origin", "getTransformOrigin", "placedSide", "placedAlign", "popperRect", "DOMRect", "fromRect", "collisionBoundariesRectWithTolerance", "rect", "amount", "width", "popperCollisions", "getCollisions", "oppositeSidePopperPoint", "getOppositeSide", "collisions", "oppositeSideCollisions", "oppositeSide", "getSideAccountingForCollisions", "anchorSize", "isHorizontalSide", "startBound", "endBound", "dimension", "isAnchorBigger", "getAlignAccountingForCollisions", "axis", "anchorStart", "anchorDimension", "popperDimension", "point", "position", "min<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "transform", "Math", "round", "window", "scrollX", "scrollY", "arrow<PERSON><PERSON><PERSON><PERSON><PERSON>", "opacity", "popperCenterX", "popperCenterY", "rotation", "arrowMaxDimension", "max", "styles", "[object Object]", "direction", "getArrowCssDirection"], "version": 3, "file": "index.module.js.map"}