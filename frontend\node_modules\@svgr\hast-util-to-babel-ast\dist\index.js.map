{"version": 3, "file": "index.js", "sources": ["../src/one.ts", "../src/all.ts", "../src/util.ts", "../src/stringToObjectStyle.ts", "../src/mappings.ts", "../src/getAttributes.ts", "../src/handlers.ts", "../src/helpers.ts", "../src/index.ts"], "sourcesContent": ["import type { Node, RootNode, ElementNode } from 'svg-parser'\nimport type { Helpers } from './helpers'\nimport type * as t from '@babel/types'\n\nexport const one = (\n  h: Helpers,\n  node: Node,\n  parent?: RootNode | ElementNode,\n): t.JSXElement | t.ExpressionStatement | t.JSXExpressionContainer | null => {\n  const type = node && node.type\n  const fn = h.handlers[type]\n\n  /* Fail on non-nodes. */\n  if (!type) {\n    throw new Error(`Expected node, got \\`${node}\\``)\n  }\n\n  if (!fn) {\n    throw new Error(`Node of type ${type} is unknown`)\n  }\n\n  // @ts-ignore\n  return fn(h, node, parent)\n}\n", "import { one } from './one'\nimport type * as t from '@babel/types'\nimport type { RootNode, ElementNode } from 'svg-parser'\nimport type { Helpers } from './helpers'\n\n/* Transform the children of `parent`. */\nexport const all = (\n  helpers: Helpers,\n  parent: RootNode | ElementNode,\n): (t.JSXElement | t.JSXExpressionContainer)[] => {\n  const nodes = parent.children || []\n  const { length } = nodes\n  const values = []\n  let index = -1\n\n  while (++index < length) {\n    const node = nodes[index]\n    if (typeof node !== 'string') {\n      const result = one(helpers, node, parent)\n      values.push(result)\n    }\n  }\n\n  return values.filter(Boolean) as (t.JSXElement | t.JSXExpressionContainer)[]\n}\n", "/**\n * Determines if the specified string consists entirely of numeric characters.\n */\nexport const isNumeric = (value: number | string): boolean => {\n  // @ts-ignore\n  return !Number.isNaN(value - parseFloat(value))\n}\n\n/**\n * Convert a hyphenated string to camelCase.\n */\nexport const hyphenToCamelCase = (string: string): string => {\n  return string.replace(/-(.)/g, (_, chr) => chr.toUpperCase())\n}\n\n/**\n * Trim the specified substring off the string. If the string does not end\n * with the specified substring, this is a no-op.\n *\n * @param {string} haystack String to search in\n * @param {string} needle   String to search for\n */\nexport const trimEnd = (haystack: string, needle: string): string => {\n  return haystack.endsWith(needle)\n    ? haystack.slice(0, -needle.length)\n    : haystack\n}\n\nconst KEBAB_REGEX = /[A-Z\\u00C0-\\u00D6\\u00D8-\\u00DE]/g\n\nexport const kebabCase = (str: string): string => {\n  return str.replace(KEBAB_REGEX, (match) => `-${match.toLowerCase()}`)\n}\n\nconst SPACES_REGEXP = /[\\t\\r\\n\\u0085\\u2028\\u2029]+/g\n\nexport const replaceSpaces = (str: string): string => {\n  return str.replace(SPACES_REGEXP, ' ')\n}\n", "// Inspired by https://github.com/reactjs/react-magic/blob/master/src/htmltojsx.js\nimport * as t from '@babel/types'\nimport { hyphenToCamelCase, isNumeric, trimEnd } from './util'\n\nconst PX_REGEX = /^\\d+px$/\nconst MS_REGEX = /^-ms-/\nconst VAR_REGEX = /^--/\n\n/**\n * Determines if the CSS value can be converted from a\n * 'px' suffixed string to a numeric value.\n */\nconst isConvertiblePixelValue = (value: string) => {\n  return PX_REGEX.test(value)\n}\n\n/**\n * Format style key into JSX style object key.\n */\nconst formatKey = (key: string) => {\n  if (VAR_REGEX.test(key)) {\n    return t.stringLiteral(key)\n  }\n  key = key.toLowerCase()\n  // Don't capitalize -ms- prefix\n  if (MS_REGEX.test(key)) key = key.substr(1)\n  return t.identifier(hyphenToCamelCase(key))\n}\n\n/**\n * Format style value into JSX style object value.\n */\nconst formatValue = (value: string) => {\n  if (isNumeric(value)) return t.numericLiteral(Number(value))\n  if (isConvertiblePixelValue(value))\n    return t.numericLiteral(Number(trimEnd(value, 'px')))\n  return t.stringLiteral(value)\n}\n\n/**\n * Handle parsing of inline styles.\n */\nexport const stringToObjectStyle = (rawStyle: string): t.ObjectExpression => {\n  const entries = rawStyle.split(';')\n  const properties = []\n\n  let index = -1\n\n  while (++index < entries.length) {\n    const entry = entries[index]\n    const style = entry.trim()\n    const firstColon = style.indexOf(':')\n    const value = style.substr(firstColon + 1).trim()\n    const key = style.substr(0, firstColon)\n    if (key !== '') {\n      const property = t.objectProperty(formatKey(key), formatValue(value))\n      properties.push(property)\n    }\n  }\n\n  return t.objectExpression(properties)\n}\n", "// From https://raw.githubusercontent.com/facebook/react/master/packages/react-dom/src/shared/possibleStandardNames.js\nexport const ATTRIBUTE_MAPPING = {\n  // HTML\n  accept: 'accept',\n  acceptcharset: 'acceptCharset',\n  'accept-charset': 'acceptCharset',\n  accesskey: 'accessKey',\n  action: 'action',\n  allowfullscreen: 'allowFullScreen',\n  alt: 'alt',\n  as: 'as',\n  async: 'async',\n  autocapitalize: 'autoCapitalize',\n  autocomplete: 'autoComplete',\n  autocorrect: 'autoCorrect',\n  autofocus: 'autoFocus',\n  autoplay: 'autoPlay',\n  autosave: 'autoSave',\n  capture: 'capture',\n  cellpadding: 'cellPadding',\n  cellspacing: 'cellSpacing',\n  challenge: 'challenge',\n  charset: 'charSet',\n  checked: 'checked',\n  children: 'children',\n  cite: 'cite',\n  class: 'className',\n  classid: 'classID',\n  classname: 'className',\n  cols: 'cols',\n  colspan: 'colSpan',\n  content: 'content',\n  contenteditable: 'contentEditable',\n  contextmenu: 'contextMenu',\n  controls: 'controls',\n  controlslist: 'controlsList',\n  coords: 'coords',\n  crossorigin: 'crossOrigin',\n  dangerouslysetinnerhtml: 'dangerouslySetInnerHTML',\n  data: 'data',\n  datetime: 'dateTime',\n  default: 'default',\n  defaultchecked: 'defaultChecked',\n  defaultvalue: 'defaultValue',\n  defer: 'defer',\n  dir: 'dir',\n  disabled: 'disabled',\n  download: 'download',\n  draggable: 'draggable',\n  enctype: 'encType',\n  for: 'htmlFor',\n  form: 'form',\n  formmethod: 'formMethod',\n  formaction: 'formAction',\n  formenctype: 'formEncType',\n  formnovalidate: 'formNoValidate',\n  formtarget: 'formTarget',\n  frameborder: 'frameBorder',\n  headers: 'headers',\n  height: 'height',\n  hidden: 'hidden',\n  high: 'high',\n  href: 'href',\n  hreflang: 'hrefLang',\n  htmlfor: 'htmlFor',\n  httpequiv: 'httpEquiv',\n  'http-equiv': 'httpEquiv',\n  icon: 'icon',\n  id: 'id',\n  innerhtml: 'innerHTML',\n  inputmode: 'inputMode',\n  integrity: 'integrity',\n  is: 'is',\n  itemid: 'itemID',\n  itemprop: 'itemProp',\n  itemref: 'itemRef',\n  itemscope: 'itemScope',\n  itemtype: 'itemType',\n  keyparams: 'keyParams',\n  keytype: 'keyType',\n  kind: 'kind',\n  label: 'label',\n  lang: 'lang',\n  list: 'list',\n  loop: 'loop',\n  low: 'low',\n  manifest: 'manifest',\n  marginwidth: 'marginWidth',\n  marginheight: 'marginHeight',\n  max: 'max',\n  maxlength: 'maxLength',\n  media: 'media',\n  mediagroup: 'mediaGroup',\n  method: 'method',\n  min: 'min',\n  minlength: 'minLength',\n  multiple: 'multiple',\n  muted: 'muted',\n  name: 'name',\n  nomodule: 'noModule',\n  nonce: 'nonce',\n  novalidate: 'noValidate',\n  open: 'open',\n  optimum: 'optimum',\n  pattern: 'pattern',\n  placeholder: 'placeholder',\n  playsinline: 'playsInline',\n  poster: 'poster',\n  preload: 'preload',\n  profile: 'profile',\n  radiogroup: 'radioGroup',\n  readonly: 'readOnly',\n  referrerpolicy: 'referrerPolicy',\n  rel: 'rel',\n  required: 'required',\n  reversed: 'reversed',\n  role: 'role',\n  rows: 'rows',\n  rowspan: 'rowSpan',\n  sandbox: 'sandbox',\n  scope: 'scope',\n  scoped: 'scoped',\n  scrolling: 'scrolling',\n  seamless: 'seamless',\n  selected: 'selected',\n  shape: 'shape',\n  size: 'size',\n  sizes: 'sizes',\n  span: 'span',\n  spellcheck: 'spellCheck',\n  src: 'src',\n  srcdoc: 'srcDoc',\n  srclang: 'srcLang',\n  srcset: 'srcSet',\n  start: 'start',\n  step: 'step',\n  style: 'style',\n  summary: 'summary',\n  tabindex: 'tabIndex',\n  target: 'target',\n  title: 'title',\n  type: 'type',\n  usemap: 'useMap',\n  value: 'value',\n  width: 'width',\n  wmode: 'wmode',\n  wrap: 'wrap',\n\n  // SVG\n  about: 'about',\n  accentheight: 'accentHeight',\n  'accent-height': 'accentHeight',\n  accumulate: 'accumulate',\n  additive: 'additive',\n  alignmentbaseline: 'alignmentBaseline',\n  'alignment-baseline': 'alignmentBaseline',\n  allowreorder: 'allowReorder',\n  alphabetic: 'alphabetic',\n  amplitude: 'amplitude',\n  arabicform: 'arabicForm',\n  'arabic-form': 'arabicForm',\n  ascent: 'ascent',\n  attributename: 'attributeName',\n  attributetype: 'attributeType',\n  autoreverse: 'autoReverse',\n  azimuth: 'azimuth',\n  basefrequency: 'baseFrequency',\n  baselineshift: 'baselineShift',\n  'baseline-shift': 'baselineShift',\n  baseprofile: 'baseProfile',\n  bbox: 'bbox',\n  begin: 'begin',\n  bias: 'bias',\n  by: 'by',\n  calcmode: 'calcMode',\n  capheight: 'capHeight',\n  'cap-height': 'capHeight',\n  clip: 'clip',\n  clippath: 'clipPath',\n  'clip-path': 'clipPath',\n  clippathunits: 'clipPathUnits',\n  cliprule: 'clipRule',\n  'clip-rule': 'clipRule',\n  color: 'color',\n  colorinterpolation: 'colorInterpolation',\n  'color-interpolation': 'colorInterpolation',\n  colorinterpolationfilters: 'colorInterpolationFilters',\n  'color-interpolation-filters': 'colorInterpolationFilters',\n  colorprofile: 'colorProfile',\n  'color-profile': 'colorProfile',\n  colorrendering: 'colorRendering',\n  'color-rendering': 'colorRendering',\n  contentscripttype: 'contentScriptType',\n  contentstyletype: 'contentStyleType',\n  cursor: 'cursor',\n  cx: 'cx',\n  cy: 'cy',\n  d: 'd',\n  datatype: 'datatype',\n  decelerate: 'decelerate',\n  descent: 'descent',\n  diffuseconstant: 'diffuseConstant',\n  direction: 'direction',\n  display: 'display',\n  divisor: 'divisor',\n  dominantbaseline: 'dominantBaseline',\n  'dominant-baseline': 'dominantBaseline',\n  dur: 'dur',\n  dx: 'dx',\n  dy: 'dy',\n  edgemode: 'edgeMode',\n  elevation: 'elevation',\n  enablebackground: 'enableBackground',\n  'enable-background': 'enableBackground',\n  end: 'end',\n  exponent: 'exponent',\n  externalresourcesrequired: 'externalResourcesRequired',\n  fill: 'fill',\n  fillopacity: 'fillOpacity',\n  'fill-opacity': 'fillOpacity',\n  fillrule: 'fillRule',\n  'fill-rule': 'fillRule',\n  filter: 'filter',\n  filterres: 'filterRes',\n  filterunits: 'filterUnits',\n  floodopacity: 'floodOpacity',\n  'flood-opacity': 'floodOpacity',\n  floodcolor: 'floodColor',\n  'flood-color': 'floodColor',\n  focusable: 'focusable',\n  fontfamily: 'fontFamily',\n  'font-family': 'fontFamily',\n  fontsize: 'fontSize',\n  'font-size': 'fontSize',\n  fontsizeadjust: 'fontSizeAdjust',\n  'font-size-adjust': 'fontSizeAdjust',\n  fontstretch: 'fontStretch',\n  'font-stretch': 'fontStretch',\n  fontstyle: 'fontStyle',\n  'font-style': 'fontStyle',\n  fontvariant: 'fontVariant',\n  'font-variant': 'fontVariant',\n  fontweight: 'fontWeight',\n  'font-weight': 'fontWeight',\n  format: 'format',\n  from: 'from',\n  fx: 'fx',\n  fy: 'fy',\n  g1: 'g1',\n  g2: 'g2',\n  glyphname: 'glyphName',\n  'glyph-name': 'glyphName',\n  glyphorientationhorizontal: 'glyphOrientationHorizontal',\n  'glyph-orientation-horizontal': 'glyphOrientationHorizontal',\n  glyphorientationvertical: 'glyphOrientationVertical',\n  'glyph-orientation-vertical': 'glyphOrientationVertical',\n  glyphref: 'glyphRef',\n  gradienttransform: 'gradientTransform',\n  gradientunits: 'gradientUnits',\n  hanging: 'hanging',\n  horizadvx: 'horizAdvX',\n  'horiz-adv-x': 'horizAdvX',\n  horizoriginx: 'horizOriginX',\n  'horiz-origin-x': 'horizOriginX',\n  ideographic: 'ideographic',\n  imagerendering: 'imageRendering',\n  'image-rendering': 'imageRendering',\n  in2: 'in2',\n  in: 'in',\n  inlist: 'inlist',\n  intercept: 'intercept',\n  k1: 'k1',\n  k2: 'k2',\n  k3: 'k3',\n  k4: 'k4',\n  k: 'k',\n  kernelmatrix: 'kernelMatrix',\n  kernelunitlength: 'kernelUnitLength',\n  kerning: 'kerning',\n  keypoints: 'keyPoints',\n  keysplines: 'keySplines',\n  keytimes: 'keyTimes',\n  lengthadjust: 'lengthAdjust',\n  letterspacing: 'letterSpacing',\n  'letter-spacing': 'letterSpacing',\n  lightingcolor: 'lightingColor',\n  'lighting-color': 'lightingColor',\n  limitingconeangle: 'limitingConeAngle',\n  local: 'local',\n  markerend: 'markerEnd',\n  'marker-end': 'markerEnd',\n  markerheight: 'markerHeight',\n  markermid: 'markerMid',\n  'marker-mid': 'markerMid',\n  markerstart: 'markerStart',\n  'marker-start': 'markerStart',\n  markerunits: 'markerUnits',\n  markerwidth: 'markerWidth',\n  mask: 'mask',\n  maskcontentunits: 'maskContentUnits',\n  maskunits: 'maskUnits',\n  mathematical: 'mathematical',\n  mode: 'mode',\n  numoctaves: 'numOctaves',\n  offset: 'offset',\n  opacity: 'opacity',\n  operator: 'operator',\n  order: 'order',\n  orient: 'orient',\n  orientation: 'orientation',\n  origin: 'origin',\n  overflow: 'overflow',\n  overlineposition: 'overlinePosition',\n  'overline-position': 'overlinePosition',\n  overlinethickness: 'overlineThickness',\n  'overline-thickness': 'overlineThickness',\n  paintorder: 'paintOrder',\n  'paint-order': 'paintOrder',\n  panose1: 'panose1',\n  'panose-1': 'panose1',\n  pathlength: 'pathLength',\n  patterncontentunits: 'patternContentUnits',\n  patterntransform: 'patternTransform',\n  patternunits: 'patternUnits',\n  pointerevents: 'pointerEvents',\n  'pointer-events': 'pointerEvents',\n  points: 'points',\n  pointsatx: 'pointsAtX',\n  pointsaty: 'pointsAtY',\n  pointsatz: 'pointsAtZ',\n  prefix: 'prefix',\n  preservealpha: 'preserveAlpha',\n  preserveaspectratio: 'preserveAspectRatio',\n  primitiveunits: 'primitiveUnits',\n  property: 'property',\n  r: 'r',\n  radius: 'radius',\n  refx: 'refX',\n  refy: 'refY',\n  renderingintent: 'renderingIntent',\n  'rendering-intent': 'renderingIntent',\n  repeatcount: 'repeatCount',\n  repeatdur: 'repeatDur',\n  requiredextensions: 'requiredExtensions',\n  requiredfeatures: 'requiredFeatures',\n  resource: 'resource',\n  restart: 'restart',\n  result: 'result',\n  results: 'results',\n  rotate: 'rotate',\n  rx: 'rx',\n  ry: 'ry',\n  scale: 'scale',\n  security: 'security',\n  seed: 'seed',\n  shaperendering: 'shapeRendering',\n  'shape-rendering': 'shapeRendering',\n  slope: 'slope',\n  spacing: 'spacing',\n  specularconstant: 'specularConstant',\n  specularexponent: 'specularExponent',\n  speed: 'speed',\n  spreadmethod: 'spreadMethod',\n  startoffset: 'startOffset',\n  stddeviation: 'stdDeviation',\n  stemh: 'stemh',\n  stemv: 'stemv',\n  stitchtiles: 'stitchTiles',\n  stopcolor: 'stopColor',\n  'stop-color': 'stopColor',\n  stopopacity: 'stopOpacity',\n  'stop-opacity': 'stopOpacity',\n  strikethroughposition: 'strikethroughPosition',\n  'strikethrough-position': 'strikethroughPosition',\n  strikethroughthickness: 'strikethroughThickness',\n  'strikethrough-thickness': 'strikethroughThickness',\n  string: 'string',\n  stroke: 'stroke',\n  strokedasharray: 'strokeDasharray',\n  'stroke-dasharray': 'strokeDasharray',\n  strokedashoffset: 'strokeDashoffset',\n  'stroke-dashoffset': 'strokeDashoffset',\n  strokelinecap: 'strokeLinecap',\n  'stroke-linecap': 'strokeLinecap',\n  strokelinejoin: 'strokeLinejoin',\n  'stroke-linejoin': 'strokeLinejoin',\n  strokemiterlimit: 'strokeMiterlimit',\n  'stroke-miterlimit': 'strokeMiterlimit',\n  strokewidth: 'strokeWidth',\n  'stroke-width': 'strokeWidth',\n  strokeopacity: 'strokeOpacity',\n  'stroke-opacity': 'strokeOpacity',\n  suppresscontenteditablewarning: 'suppressContentEditableWarning',\n  suppresshydrationwarning: 'suppressHydrationWarning',\n  surfacescale: 'surfaceScale',\n  systemlanguage: 'systemLanguage',\n  tablevalues: 'tableValues',\n  targetx: 'targetX',\n  targety: 'targetY',\n  textanchor: 'textAnchor',\n  'text-anchor': 'textAnchor',\n  textdecoration: 'textDecoration',\n  'text-decoration': 'textDecoration',\n  textlength: 'textLength',\n  textrendering: 'textRendering',\n  'text-rendering': 'textRendering',\n  to: 'to',\n  transform: 'transform',\n  typeof: 'typeof',\n  u1: 'u1',\n  u2: 'u2',\n  underlineposition: 'underlinePosition',\n  'underline-position': 'underlinePosition',\n  underlinethickness: 'underlineThickness',\n  'underline-thickness': 'underlineThickness',\n  unicode: 'unicode',\n  unicodebidi: 'unicodeBidi',\n  'unicode-bidi': 'unicodeBidi',\n  unicoderange: 'unicodeRange',\n  'unicode-range': 'unicodeRange',\n  unitsperem: 'unitsPerEm',\n  'units-per-em': 'unitsPerEm',\n  unselectable: 'unselectable',\n  valphabetic: 'vAlphabetic',\n  'v-alphabetic': 'vAlphabetic',\n  values: 'values',\n  vectoreffect: 'vectorEffect',\n  'vector-effect': 'vectorEffect',\n  version: 'version',\n  vertadvy: 'vertAdvY',\n  'vert-adv-y': 'vertAdvY',\n  vertoriginx: 'vertOriginX',\n  'vert-origin-x': 'vertOriginX',\n  vertoriginy: 'vertOriginY',\n  'vert-origin-y': 'vertOriginY',\n  vhanging: 'vHanging',\n  'v-hanging': 'vHanging',\n  videographic: 'vIdeographic',\n  'v-ideographic': 'vIdeographic',\n  viewbox: 'viewBox',\n  viewtarget: 'viewTarget',\n  visibility: 'visibility',\n  vmathematical: 'vMathematical',\n  'v-mathematical': 'vMathematical',\n  vocab: 'vocab',\n  widths: 'widths',\n  wordspacing: 'wordSpacing',\n  'word-spacing': 'wordSpacing',\n  writingmode: 'writingMode',\n  'writing-mode': 'writingMode',\n  x1: 'x1',\n  x2: 'x2',\n  x: 'x',\n  xchannelselector: 'xChannelSelector',\n  xheight: 'xHeight',\n  'x-height': 'xHeight',\n  xlinkactuate: 'xlinkActuate',\n  'xlink:actuate': 'xlinkActuate',\n  xlinkarcrole: 'xlinkArcrole',\n  'xlink:arcrole': 'xlinkArcrole',\n  xlinkhref: 'xlinkHref',\n  'xlink:href': 'xlinkHref',\n  xlinkrole: 'xlinkRole',\n  'xlink:role': 'xlinkRole',\n  xlinkshow: 'xlinkShow',\n  'xlink:show': 'xlinkShow',\n  xlinktitle: 'xlinkTitle',\n  'xlink:title': 'xlinkTitle',\n  xlinktype: 'xlinkType',\n  'xlink:type': 'xlinkType',\n  xmlbase: 'xmlBase',\n  'xml:base': 'xmlBase',\n  xmllang: 'xmlLang',\n  'xml:lang': 'xmlLang',\n  xmlns: 'xmlns',\n  'xml:space': 'xmlSpace',\n  xmlnsxlink: 'xmlnsXlink',\n  'xmlns:xlink': 'xmlnsXlink',\n  xmlspace: 'xmlSpace',\n  y1: 'y1',\n  y2: 'y2',\n  y: 'y',\n  ychannelselector: 'yChannelSelector',\n  z: 'z',\n  zoomandpan: 'zoomAndPan',\n}\n\nexport const ELEMENT_ATTRIBUTE_MAPPING = {\n  input: {\n    checked: 'defaultChecked',\n    value: 'defaultValue',\n    maxlength: 'maxLength',\n  },\n  form: {\n    enctype: 'encType',\n  },\n}\n\n// Reference: https://developer.mozilla.org/en-US/docs/Web/SVG/Element#SVG_elements\nexport const ELEMENT_TAG_NAME_MAPPING: Record<string, string> = {\n  a: 'a',\n  altglyph: 'altGlyph',\n  altglyphdef: 'altGlyphDef',\n  altglyphitem: 'altGlyphItem',\n  animate: 'animate',\n  animatecolor: 'animateColor',\n  animatemotion: 'animateMotion',\n  animatetransform: 'animateTransform',\n  audio: 'audio',\n  canvas: 'canvas',\n  circle: 'circle',\n  clippath: 'clipPath',\n  'color-profile': 'colorProfile',\n  cursor: 'cursor',\n  defs: 'defs',\n  desc: 'desc',\n  discard: 'discard',\n  ellipse: 'ellipse',\n  feblend: 'feBlend',\n  fecolormatrix: 'feColorMatrix',\n  fecomponenttransfer: 'feComponentTransfer',\n  fecomposite: 'feComposite',\n  feconvolvematrix: 'feConvolveMatrix',\n  fediffuselighting: 'feDiffuseLighting',\n  fedisplacementmap: 'feDisplacementMap',\n  fedistantlight: 'feDistantLight',\n  fedropshadow: 'feDropShadow',\n  feflood: 'feFlood',\n  fefunca: 'feFuncA',\n  fefuncb: 'feFuncB',\n  fefuncg: 'feFuncG',\n  fefuncr: 'feFuncR',\n  fegaussianblur: 'feGaussianBlur',\n  feimage: 'feImage',\n  femerge: 'feMerge',\n  femergenode: 'feMergeNode',\n  femorphology: 'feMorphology',\n  feoffset: 'feOffset',\n  fepointlight: 'fePointLight',\n  fespecularlighting: 'feSpecularLighting',\n  fespotlight: 'feSpotLight',\n  fetile: 'feTile',\n  feturbulence: 'feTurbulence',\n  filter: 'filter',\n  font: 'font',\n  'font-face': 'fontFace',\n  'font-face-format': 'fontFaceFormat',\n  'font-face-name': 'fontFaceName',\n  'font-face-src': 'fontFaceSrc',\n  'font-face-uri': 'fontFaceUri',\n  foreignobject: 'foreignObject',\n  g: 'g',\n  glyph: 'glyph',\n  glyphref: 'glyphRef',\n  hatch: 'hatch',\n  hatchpath: 'hatchpath',\n  hkern: 'hkern',\n  iframe: 'iframe',\n  image: 'image',\n  line: 'line',\n  lineargradient: 'linearGradient',\n  marker: 'marker',\n  mask: 'mask',\n  mesh: 'mesh',\n  meshgradient: 'meshgradient',\n  meshpatch: 'meshpatch',\n  meshrow: 'meshrow',\n  metadata: 'metadata',\n  'missing-glyph': 'missingGlyph',\n  mpath: 'mpath',\n  path: 'path',\n  pattern: 'pattern',\n  polygon: 'polygon',\n  polyline: 'polyline',\n  radialgradient: 'radialGradient',\n  rect: 'rect',\n  script: 'script',\n  set: 'set',\n  solidcolor: 'solidcolor',\n  stop: 'stop',\n  style: 'style',\n  svg: 'svg',\n  switch: 'switch',\n  symbol: 'symbol',\n  text: 'text',\n  textpath: 'textPath',\n  title: 'title',\n  tref: 'tref',\n  tspan: 'tspan',\n  unknown: 'unknown',\n  use: 'use',\n  video: 'video',\n  view: 'view',\n  vkern: 'vkern',\n}\n", "import * as t from '@babel/types'\nimport type { ElementNode } from 'svg-parser'\nimport { isNumeric, kebabCase, replaceSpaces } from './util'\nimport { stringToObjectStyle } from './stringToObjectStyle'\nimport { ATTRIBUTE_MAPPING, ELEMENT_ATTRIBUTE_MAPPING } from './mappings'\n\nconst convertAriaAttribute = (kebabKey: string) => {\n  const [aria, ...parts] = kebabKey.split('-')\n  return `${aria}-${parts.join('').toLowerCase()}`\n}\n\nconst getKey = (key: string, node: ElementNode) => {\n  const lowerCaseKey = key.toLowerCase()\n  const mappedElementAttribute =\n    // @ts-ignore\n    ELEMENT_ATTRIBUTE_MAPPING[node.name] &&\n    // @ts-ignore\n    ELEMENT_ATTRIBUTE_MAPPING[node.name][lowerCaseKey]\n  // @ts-ignore\n  const mappedAttribute = ATTRIBUTE_MAPPING[lowerCaseKey]\n\n  if (mappedElementAttribute || mappedAttribute) {\n    return t.jsxIdentifier(mappedElementAttribute || mappedAttribute)\n  }\n\n  const kebabKey = kebabCase(key)\n\n  if (kebabKey.startsWith('aria-')) {\n    return t.jsxIdentifier(convertAriaAttribute(kebabKey))\n  }\n\n  if (kebabKey.startsWith('data-')) {\n    return t.jsxIdentifier(kebabKey)\n  }\n\n  return t.jsxIdentifier(key)\n}\n\nconst getValue = (key: string, value: string[] | string | number) => {\n  // Handle className\n  if (Array.isArray(value)) {\n    return t.stringLiteral(replaceSpaces(value.join(' ')))\n  }\n\n  if (key === 'style') {\n    return t.jsxExpressionContainer(stringToObjectStyle(value as string))\n  }\n\n  if (typeof value === 'number' || isNumeric(value)) {\n    return t.jsxExpressionContainer(t.numericLiteral(Number(value)))\n  }\n\n  return t.stringLiteral(replaceSpaces(value))\n}\n\nexport const getAttributes = (node: ElementNode): t.JSXAttribute[] => {\n  if (!node.properties) return []\n  const keys = Object.keys(node.properties)\n  const attributes = []\n  let index = -1\n\n  while (++index < keys.length) {\n    const key = keys[index]\n    const value = node.properties[key]\n    const attribute = t.jsxAttribute(getKey(key, node), getValue(key, value))\n    attributes.push(attribute)\n  }\n\n  return attributes\n}\n", "import * as t from '@babel/types'\nimport { decodeXML } from 'entities'\nimport { all } from './all'\nimport { getAttributes } from './getAttributes'\nimport { ELEMENT_TAG_NAME_MAPPING } from './mappings'\nimport type { RootNode, ElementNode, TextNode } from 'svg-parser'\nimport type { Helpers } from './helpers'\n\nexport const root = (h: Helpers, node: RootNode): t.Program =>\n  // @ts-ignore\n  t.program(all(h, node))\n\nexport const comment = (\n  _: Helpers,\n  node: ElementNode,\n  parent: RootNode | ElementNode,\n): t.JSXExpressionContainer | null => {\n  if (parent.type === 'root' || !node.value) return null\n\n  const expression = t.jsxEmptyExpression()\n  t.addComment(expression, 'inner', node.value)\n  return t.jsxExpressionContainer(expression)\n}\n\nconst SPACE_REGEX = /^\\s+$/\n\nexport const text = (\n  h: Helpers,\n  node: TextNode,\n  parent: RootNode | ElementNode,\n): t.JSXExpressionContainer | null => {\n  if (parent.type === 'root') return null\n  if (typeof node.value === 'string' && SPACE_REGEX.test(node.value))\n    return null\n\n  return t.jsxExpressionContainer(\n    t.stringLiteral(decodeXML(String(node.value))),\n  )\n}\n\nexport const element = (\n  h: Helpers,\n  node: ElementNode,\n  parent: RootNode | ElementNode,\n): t.JSXElement | t.ExpressionStatement | null => {\n  if (!node.tagName) return null\n\n  const children = all(h, node)\n  const selfClosing = children.length === 0\n\n  const name = ELEMENT_TAG_NAME_MAPPING[node.tagName] || node.tagName\n\n  const openingElement = t.jsxOpeningElement(\n    t.jsxIdentifier(name),\n    getAttributes(node),\n    selfClosing,\n  )\n\n  const closingElement = !selfClosing\n    ? t.jsxClosingElement(t.jsxIdentifier(name))\n    : null\n\n  const jsxElement = t.jsxElement(openingElement, closingElement, children)\n\n  if (parent.type === 'root') {\n    return t.expressionStatement(jsxElement)\n  }\n\n  return jsxElement\n}\n", "import * as handlers from './handlers'\n\nexport const helpers = { handlers }\n\nexport type Helpers = typeof helpers\n", "import type { RootNode } from 'svg-parser'\nimport type * as t from '@babel/types'\nimport { root } from './handlers'\nimport { helpers } from './helpers'\n\nconst toBabelAST = (tree: RootNode): t.Program => root(helpers, tree)\n\nexport default toBabelAST\n"], "names": ["t", "decodeXML"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAIO,MAAM,GAAM,GAAA,CACjB,CACA,EAAA,IAAA,EACA,MAC2E,KAAA;AAC3E,EAAM,MAAA,IAAA,GAAO,QAAQ,IAAK,CAAA,IAAA,CAAA;AAC1B,EAAM,MAAA,EAAA,GAAK,EAAE,QAAS,CAAA,IAAA,CAAA,CAAA;AAGtB,EAAA,IAAI,CAAC,IAAM,EAAA;AACT,IAAM,MAAA,IAAI,KAAM,CAAA,CAAA,qBAAA,EAAwB,IAAQ,CAAA,EAAA,CAAA,CAAA,CAAA;AAAA,GAClD;AAEA,EAAA,IAAI,CAAC,EAAI,EAAA;AACP,IAAM,MAAA,IAAI,KAAM,CAAA,CAAA,aAAA,EAAgB,IAAiB,CAAA,WAAA,CAAA,CAAA,CAAA;AAAA,GACnD;AAGA,EAAO,OAAA,EAAA,CAAG,CAAG,EAAA,IAAA,EAAM,MAAM,CAAA,CAAA;AAC3B,CAAA;;ACjBa,MAAA,GAAA,GAAM,CACjB,OAAA,EACA,MACgD,KAAA;AAChD,EAAM,MAAA,KAAA,GAAQ,MAAO,CAAA,QAAA,IAAY,EAAC,CAAA;AAClC,EAAM,MAAA,EAAE,QAAW,GAAA,KAAA,CAAA;AACnB,EAAA,MAAM,SAAS,EAAC,CAAA;AAChB,EAAA,IAAI,KAAQ,GAAA,CAAA,CAAA,CAAA;AAEZ,EAAO,OAAA,EAAE,QAAQ,MAAQ,EAAA;AACvB,IAAA,MAAM,OAAO,KAAM,CAAA,KAAA,CAAA,CAAA;AACnB,IAAI,IAAA,OAAO,SAAS,QAAU,EAAA;AAC5B,MAAA,MAAM,MAAS,GAAA,GAAA,CAAI,OAAS,EAAA,IAAA,EAAM,MAAM,CAAA,CAAA;AACxC,MAAA,MAAA,CAAO,KAAK,MAAM,CAAA,CAAA;AAAA,KACpB;AAAA,GACF;AAEA,EAAO,OAAA,MAAA,CAAO,OAAO,OAAO,CAAA,CAAA;AAC9B,CAAA;;ACrBa,MAAA,SAAA,GAAY,CAAC,KAAoC,KAAA;AAE5D,EAAA,OAAO,CAAC,MAAO,CAAA,KAAA,CAAM,KAAQ,GAAA,UAAA,CAAW,KAAK,CAAC,CAAA,CAAA;AAChD,CAAA,CAAA;AAKa,MAAA,iBAAA,GAAoB,CAAC,MAA2B,KAAA;AAC3D,EAAO,OAAA,MAAA,CAAO,QAAQ,OAAS,EAAA,CAAC,GAAG,GAAQ,KAAA,GAAA,CAAI,aAAa,CAAA,CAAA;AAC9D,CAAA,CAAA;AASa,MAAA,OAAA,GAAU,CAAC,QAAA,EAAkB,MAA2B,KAAA;AACnE,EAAO,OAAA,QAAA,CAAS,QAAS,CAAA,MAAM,CAC3B,GAAA,QAAA,CAAS,MAAM,CAAG,EAAA,CAAC,MAAO,CAAA,MAAM,CAChC,GAAA,QAAA,CAAA;AACN,CAAA,CAAA;AAEA,MAAM,WAAc,GAAA,kCAAA,CAAA;AAEP,MAAA,SAAA,GAAY,CAAC,GAAwB,KAAA;AAChD,EAAO,OAAA,GAAA,CAAI,QAAQ,WAAa,EAAA,CAAC,UAAU,CAAI,CAAA,EAAA,KAAA,CAAM,aAAe,CAAA,CAAA,CAAA,CAAA;AACtE,CAAA,CAAA;AAEA,MAAM,aAAgB,GAAA,8BAAA,CAAA;AAET,MAAA,aAAA,GAAgB,CAAC,GAAwB,KAAA;AACpD,EAAO,OAAA,GAAA,CAAI,OAAQ,CAAA,aAAA,EAAe,GAAG,CAAA,CAAA;AACvC,CAAA;;AClCA,MAAM,QAAW,GAAA,SAAA,CAAA;AACjB,MAAM,QAAW,GAAA,OAAA,CAAA;AACjB,MAAM,SAAY,GAAA,KAAA,CAAA;AAMlB,MAAM,uBAAA,GAA0B,CAAC,KAAkB,KAAA;AACjD,EAAO,OAAA,QAAA,CAAS,KAAK,KAAK,CAAA,CAAA;AAC5B,CAAA,CAAA;AAKA,MAAM,SAAA,GAAY,CAAC,GAAgB,KAAA;AACjC,EAAI,IAAA,SAAA,CAAU,IAAK,CAAA,GAAG,CAAG,EAAA;AACvB,IAAO,OAAAA,YAAA,CAAE,cAAc,GAAG,CAAA,CAAA;AAAA,GAC5B;AACA,EAAA,GAAA,GAAM,IAAI,WAAY,EAAA,CAAA;AAEtB,EAAI,IAAA,QAAA,CAAS,KAAK,GAAG,CAAA;AAAG,IAAM,GAAA,GAAA,GAAA,CAAI,OAAO,CAAC,CAAA,CAAA;AAC1C,EAAA,OAAOA,YAAE,CAAA,UAAA,CAAW,iBAAkB,CAAA,GAAG,CAAC,CAAA,CAAA;AAC5C,CAAA,CAAA;AAKA,MAAM,WAAA,GAAc,CAAC,KAAkB,KAAA;AACrC,EAAA,IAAI,UAAU,KAAK,CAAA;AAAG,IAAA,OAAOA,YAAE,CAAA,cAAA,CAAe,MAAO,CAAA,KAAK,CAAC,CAAA,CAAA;AAC3D,EAAA,IAAI,wBAAwB,KAAK,CAAA;AAC/B,IAAA,OAAOA,aAAE,cAAe,CAAA,MAAA,CAAO,QAAQ,KAAO,EAAA,IAAI,CAAC,CAAC,CAAA,CAAA;AACtD,EAAO,OAAAA,YAAA,CAAE,cAAc,KAAK,CAAA,CAAA;AAC9B,CAAA,CAAA;AAKa,MAAA,mBAAA,GAAsB,CAAC,QAAyC,KAAA;AAC3E,EAAM,MAAA,OAAA,GAAU,QAAS,CAAA,KAAA,CAAM,GAAG,CAAA,CAAA;AAClC,EAAA,MAAM,aAAa,EAAC,CAAA;AAEpB,EAAA,IAAI,KAAQ,GAAA,CAAA,CAAA,CAAA;AAEZ,EAAO,OAAA,EAAE,KAAQ,GAAA,OAAA,CAAQ,MAAQ,EAAA;AAC/B,IAAA,MAAM,QAAQ,OAAQ,CAAA,KAAA,CAAA,CAAA;AACtB,IAAM,MAAA,KAAA,GAAQ,MAAM,IAAK,EAAA,CAAA;AACzB,IAAM,MAAA,UAAA,GAAa,KAAM,CAAA,OAAA,CAAQ,GAAG,CAAA,CAAA;AACpC,IAAA,MAAM,QAAQ,KAAM,CAAA,MAAA,CAAO,UAAa,GAAA,CAAC,EAAE,IAAK,EAAA,CAAA;AAChD,IAAA,MAAM,GAAM,GAAA,KAAA,CAAM,MAAO,CAAA,CAAA,EAAG,UAAU,CAAA,CAAA;AACtC,IAAA,IAAI,QAAQ,EAAI,EAAA;AACd,MAAM,MAAA,QAAA,GAAWA,aAAE,cAAe,CAAA,SAAA,CAAU,GAAG,CAAG,EAAA,WAAA,CAAY,KAAK,CAAC,CAAA,CAAA;AACpE,MAAA,UAAA,CAAW,KAAK,QAAQ,CAAA,CAAA;AAAA,KAC1B;AAAA,GACF;AAEA,EAAO,OAAAA,YAAA,CAAE,iBAAiB,UAAU,CAAA,CAAA;AACtC,CAAA;;AC5DO,MAAM,iBAAoB,GAAA;AAAA,EAE/B,MAAQ,EAAA,QAAA;AAAA,EACR,aAAe,EAAA,eAAA;AAAA,EACf,gBAAkB,EAAA,eAAA;AAAA,EAClB,SAAW,EAAA,WAAA;AAAA,EACX,MAAQ,EAAA,QAAA;AAAA,EACR,eAAiB,EAAA,iBAAA;AAAA,EACjB,GAAK,EAAA,KAAA;AAAA,EACL,EAAI,EAAA,IAAA;AAAA,EACJ,KAAO,EAAA,OAAA;AAAA,EACP,cAAgB,EAAA,gBAAA;AAAA,EAChB,YAAc,EAAA,cAAA;AAAA,EACd,WAAa,EAAA,aAAA;AAAA,EACb,SAAW,EAAA,WAAA;AAAA,EACX,QAAU,EAAA,UAAA;AAAA,EACV,QAAU,EAAA,UAAA;AAAA,EACV,OAAS,EAAA,SAAA;AAAA,EACT,WAAa,EAAA,aAAA;AAAA,EACb,WAAa,EAAA,aAAA;AAAA,EACb,SAAW,EAAA,WAAA;AAAA,EACX,OAAS,EAAA,SAAA;AAAA,EACT,OAAS,EAAA,SAAA;AAAA,EACT,QAAU,EAAA,UAAA;AAAA,EACV,IAAM,EAAA,MAAA;AAAA,EACN,KAAO,EAAA,WAAA;AAAA,EACP,OAAS,EAAA,SAAA;AAAA,EACT,SAAW,EAAA,WAAA;AAAA,EACX,IAAM,EAAA,MAAA;AAAA,EACN,OAAS,EAAA,SAAA;AAAA,EACT,OAAS,EAAA,SAAA;AAAA,EACT,eAAiB,EAAA,iBAAA;AAAA,EACjB,WAAa,EAAA,aAAA;AAAA,EACb,QAAU,EAAA,UAAA;AAAA,EACV,YAAc,EAAA,cAAA;AAAA,EACd,MAAQ,EAAA,QAAA;AAAA,EACR,WAAa,EAAA,aAAA;AAAA,EACb,uBAAyB,EAAA,yBAAA;AAAA,EACzB,IAAM,EAAA,MAAA;AAAA,EACN,QAAU,EAAA,UAAA;AAAA,EACV,OAAS,EAAA,SAAA;AAAA,EACT,cAAgB,EAAA,gBAAA;AAAA,EAChB,YAAc,EAAA,cAAA;AAAA,EACd,KAAO,EAAA,OAAA;AAAA,EACP,GAAK,EAAA,KAAA;AAAA,EACL,QAAU,EAAA,UAAA;AAAA,EACV,QAAU,EAAA,UAAA;AAAA,EACV,SAAW,EAAA,WAAA;AAAA,EACX,OAAS,EAAA,SAAA;AAAA,EACT,GAAK,EAAA,SAAA;AAAA,EACL,IAAM,EAAA,MAAA;AAAA,EACN,UAAY,EAAA,YAAA;AAAA,EACZ,UAAY,EAAA,YAAA;AAAA,EACZ,WAAa,EAAA,aAAA;AAAA,EACb,cAAgB,EAAA,gBAAA;AAAA,EAChB,UAAY,EAAA,YAAA;AAAA,EACZ,WAAa,EAAA,aAAA;AAAA,EACb,OAAS,EAAA,SAAA;AAAA,EACT,MAAQ,EAAA,QAAA;AAAA,EACR,MAAQ,EAAA,QAAA;AAAA,EACR,IAAM,EAAA,MAAA;AAAA,EACN,IAAM,EAAA,MAAA;AAAA,EACN,QAAU,EAAA,UAAA;AAAA,EACV,OAAS,EAAA,SAAA;AAAA,EACT,SAAW,EAAA,WAAA;AAAA,EACX,YAAc,EAAA,WAAA;AAAA,EACd,IAAM,EAAA,MAAA;AAAA,EACN,EAAI,EAAA,IAAA;AAAA,EACJ,SAAW,EAAA,WAAA;AAAA,EACX,SAAW,EAAA,WAAA;AAAA,EACX,SAAW,EAAA,WAAA;AAAA,EACX,EAAI,EAAA,IAAA;AAAA,EACJ,MAAQ,EAAA,QAAA;AAAA,EACR,QAAU,EAAA,UAAA;AAAA,EACV,OAAS,EAAA,SAAA;AAAA,EACT,SAAW,EAAA,WAAA;AAAA,EACX,QAAU,EAAA,UAAA;AAAA,EACV,SAAW,EAAA,WAAA;AAAA,EACX,OAAS,EAAA,SAAA;AAAA,EACT,IAAM,EAAA,MAAA;AAAA,EACN,KAAO,EAAA,OAAA;AAAA,EACP,IAAM,EAAA,MAAA;AAAA,EACN,IAAM,EAAA,MAAA;AAAA,EACN,IAAM,EAAA,MAAA;AAAA,EACN,GAAK,EAAA,KAAA;AAAA,EACL,QAAU,EAAA,UAAA;AAAA,EACV,WAAa,EAAA,aAAA;AAAA,EACb,YAAc,EAAA,cAAA;AAAA,EACd,GAAK,EAAA,KAAA;AAAA,EACL,SAAW,EAAA,WAAA;AAAA,EACX,KAAO,EAAA,OAAA;AAAA,EACP,UAAY,EAAA,YAAA;AAAA,EACZ,MAAQ,EAAA,QAAA;AAAA,EACR,GAAK,EAAA,KAAA;AAAA,EACL,SAAW,EAAA,WAAA;AAAA,EACX,QAAU,EAAA,UAAA;AAAA,EACV,KAAO,EAAA,OAAA;AAAA,EACP,IAAM,EAAA,MAAA;AAAA,EACN,QAAU,EAAA,UAAA;AAAA,EACV,KAAO,EAAA,OAAA;AAAA,EACP,UAAY,EAAA,YAAA;AAAA,EACZ,IAAM,EAAA,MAAA;AAAA,EACN,OAAS,EAAA,SAAA;AAAA,EACT,OAAS,EAAA,SAAA;AAAA,EACT,WAAa,EAAA,aAAA;AAAA,EACb,WAAa,EAAA,aAAA;AAAA,EACb,MAAQ,EAAA,QAAA;AAAA,EACR,OAAS,EAAA,SAAA;AAAA,EACT,OAAS,EAAA,SAAA;AAAA,EACT,UAAY,EAAA,YAAA;AAAA,EACZ,QAAU,EAAA,UAAA;AAAA,EACV,cAAgB,EAAA,gBAAA;AAAA,EAChB,GAAK,EAAA,KAAA;AAAA,EACL,QAAU,EAAA,UAAA;AAAA,EACV,QAAU,EAAA,UAAA;AAAA,EACV,IAAM,EAAA,MAAA;AAAA,EACN,IAAM,EAAA,MAAA;AAAA,EACN,OAAS,EAAA,SAAA;AAAA,EACT,OAAS,EAAA,SAAA;AAAA,EACT,KAAO,EAAA,OAAA;AAAA,EACP,MAAQ,EAAA,QAAA;AAAA,EACR,SAAW,EAAA,WAAA;AAAA,EACX,QAAU,EAAA,UAAA;AAAA,EACV,QAAU,EAAA,UAAA;AAAA,EACV,KAAO,EAAA,OAAA;AAAA,EACP,IAAM,EAAA,MAAA;AAAA,EACN,KAAO,EAAA,OAAA;AAAA,EACP,IAAM,EAAA,MAAA;AAAA,EACN,UAAY,EAAA,YAAA;AAAA,EACZ,GAAK,EAAA,KAAA;AAAA,EACL,MAAQ,EAAA,QAAA;AAAA,EACR,OAAS,EAAA,SAAA;AAAA,EACT,MAAQ,EAAA,QAAA;AAAA,EACR,KAAO,EAAA,OAAA;AAAA,EACP,IAAM,EAAA,MAAA;AAAA,EACN,KAAO,EAAA,OAAA;AAAA,EACP,OAAS,EAAA,SAAA;AAAA,EACT,QAAU,EAAA,UAAA;AAAA,EACV,MAAQ,EAAA,QAAA;AAAA,EACR,KAAO,EAAA,OAAA;AAAA,EACP,IAAM,EAAA,MAAA;AAAA,EACN,MAAQ,EAAA,QAAA;AAAA,EACR,KAAO,EAAA,OAAA;AAAA,EACP,KAAO,EAAA,OAAA;AAAA,EACP,KAAO,EAAA,OAAA;AAAA,EACP,IAAM,EAAA,MAAA;AAAA,EAGN,KAAO,EAAA,OAAA;AAAA,EACP,YAAc,EAAA,cAAA;AAAA,EACd,eAAiB,EAAA,cAAA;AAAA,EACjB,UAAY,EAAA,YAAA;AAAA,EACZ,QAAU,EAAA,UAAA;AAAA,EACV,iBAAmB,EAAA,mBAAA;AAAA,EACnB,oBAAsB,EAAA,mBAAA;AAAA,EACtB,YAAc,EAAA,cAAA;AAAA,EACd,UAAY,EAAA,YAAA;AAAA,EACZ,SAAW,EAAA,WAAA;AAAA,EACX,UAAY,EAAA,YAAA;AAAA,EACZ,aAAe,EAAA,YAAA;AAAA,EACf,MAAQ,EAAA,QAAA;AAAA,EACR,aAAe,EAAA,eAAA;AAAA,EACf,aAAe,EAAA,eAAA;AAAA,EACf,WAAa,EAAA,aAAA;AAAA,EACb,OAAS,EAAA,SAAA;AAAA,EACT,aAAe,EAAA,eAAA;AAAA,EACf,aAAe,EAAA,eAAA;AAAA,EACf,gBAAkB,EAAA,eAAA;AAAA,EAClB,WAAa,EAAA,aAAA;AAAA,EACb,IAAM,EAAA,MAAA;AAAA,EACN,KAAO,EAAA,OAAA;AAAA,EACP,IAAM,EAAA,MAAA;AAAA,EACN,EAAI,EAAA,IAAA;AAAA,EACJ,QAAU,EAAA,UAAA;AAAA,EACV,SAAW,EAAA,WAAA;AAAA,EACX,YAAc,EAAA,WAAA;AAAA,EACd,IAAM,EAAA,MAAA;AAAA,EACN,QAAU,EAAA,UAAA;AAAA,EACV,WAAa,EAAA,UAAA;AAAA,EACb,aAAe,EAAA,eAAA;AAAA,EACf,QAAU,EAAA,UAAA;AAAA,EACV,WAAa,EAAA,UAAA;AAAA,EACb,KAAO,EAAA,OAAA;AAAA,EACP,kBAAoB,EAAA,oBAAA;AAAA,EACpB,qBAAuB,EAAA,oBAAA;AAAA,EACvB,yBAA2B,EAAA,2BAAA;AAAA,EAC3B,6BAA+B,EAAA,2BAAA;AAAA,EAC/B,YAAc,EAAA,cAAA;AAAA,EACd,eAAiB,EAAA,cAAA;AAAA,EACjB,cAAgB,EAAA,gBAAA;AAAA,EAChB,iBAAmB,EAAA,gBAAA;AAAA,EACnB,iBAAmB,EAAA,mBAAA;AAAA,EACnB,gBAAkB,EAAA,kBAAA;AAAA,EAClB,MAAQ,EAAA,QAAA;AAAA,EACR,EAAI,EAAA,IAAA;AAAA,EACJ,EAAI,EAAA,IAAA;AAAA,EACJ,CAAG,EAAA,GAAA;AAAA,EACH,QAAU,EAAA,UAAA;AAAA,EACV,UAAY,EAAA,YAAA;AAAA,EACZ,OAAS,EAAA,SAAA;AAAA,EACT,eAAiB,EAAA,iBAAA;AAAA,EACjB,SAAW,EAAA,WAAA;AAAA,EACX,OAAS,EAAA,SAAA;AAAA,EACT,OAAS,EAAA,SAAA;AAAA,EACT,gBAAkB,EAAA,kBAAA;AAAA,EAClB,mBAAqB,EAAA,kBAAA;AAAA,EACrB,GAAK,EAAA,KAAA;AAAA,EACL,EAAI,EAAA,IAAA;AAAA,EACJ,EAAI,EAAA,IAAA;AAAA,EACJ,QAAU,EAAA,UAAA;AAAA,EACV,SAAW,EAAA,WAAA;AAAA,EACX,gBAAkB,EAAA,kBAAA;AAAA,EAClB,mBAAqB,EAAA,kBAAA;AAAA,EACrB,GAAK,EAAA,KAAA;AAAA,EACL,QAAU,EAAA,UAAA;AAAA,EACV,yBAA2B,EAAA,2BAAA;AAAA,EAC3B,IAAM,EAAA,MAAA;AAAA,EACN,WAAa,EAAA,aAAA;AAAA,EACb,cAAgB,EAAA,aAAA;AAAA,EAChB,QAAU,EAAA,UAAA;AAAA,EACV,WAAa,EAAA,UAAA;AAAA,EACb,MAAQ,EAAA,QAAA;AAAA,EACR,SAAW,EAAA,WAAA;AAAA,EACX,WAAa,EAAA,aAAA;AAAA,EACb,YAAc,EAAA,cAAA;AAAA,EACd,eAAiB,EAAA,cAAA;AAAA,EACjB,UAAY,EAAA,YAAA;AAAA,EACZ,aAAe,EAAA,YAAA;AAAA,EACf,SAAW,EAAA,WAAA;AAAA,EACX,UAAY,EAAA,YAAA;AAAA,EACZ,aAAe,EAAA,YAAA;AAAA,EACf,QAAU,EAAA,UAAA;AAAA,EACV,WAAa,EAAA,UAAA;AAAA,EACb,cAAgB,EAAA,gBAAA;AAAA,EAChB,kBAAoB,EAAA,gBAAA;AAAA,EACpB,WAAa,EAAA,aAAA;AAAA,EACb,cAAgB,EAAA,aAAA;AAAA,EAChB,SAAW,EAAA,WAAA;AAAA,EACX,YAAc,EAAA,WAAA;AAAA,EACd,WAAa,EAAA,aAAA;AAAA,EACb,cAAgB,EAAA,aAAA;AAAA,EAChB,UAAY,EAAA,YAAA;AAAA,EACZ,aAAe,EAAA,YAAA;AAAA,EACf,MAAQ,EAAA,QAAA;AAAA,EACR,IAAM,EAAA,MAAA;AAAA,EACN,EAAI,EAAA,IAAA;AAAA,EACJ,EAAI,EAAA,IAAA;AAAA,EACJ,EAAI,EAAA,IAAA;AAAA,EACJ,EAAI,EAAA,IAAA;AAAA,EACJ,SAAW,EAAA,WAAA;AAAA,EACX,YAAc,EAAA,WAAA;AAAA,EACd,0BAA4B,EAAA,4BAAA;AAAA,EAC5B,8BAAgC,EAAA,4BAAA;AAAA,EAChC,wBAA0B,EAAA,0BAAA;AAAA,EAC1B,4BAA8B,EAAA,0BAAA;AAAA,EAC9B,QAAU,EAAA,UAAA;AAAA,EACV,iBAAmB,EAAA,mBAAA;AAAA,EACnB,aAAe,EAAA,eAAA;AAAA,EACf,OAAS,EAAA,SAAA;AAAA,EACT,SAAW,EAAA,WAAA;AAAA,EACX,aAAe,EAAA,WAAA;AAAA,EACf,YAAc,EAAA,cAAA;AAAA,EACd,gBAAkB,EAAA,cAAA;AAAA,EAClB,WAAa,EAAA,aAAA;AAAA,EACb,cAAgB,EAAA,gBAAA;AAAA,EAChB,iBAAmB,EAAA,gBAAA;AAAA,EACnB,GAAK,EAAA,KAAA;AAAA,EACL,EAAI,EAAA,IAAA;AAAA,EACJ,MAAQ,EAAA,QAAA;AAAA,EACR,SAAW,EAAA,WAAA;AAAA,EACX,EAAI,EAAA,IAAA;AAAA,EACJ,EAAI,EAAA,IAAA;AAAA,EACJ,EAAI,EAAA,IAAA;AAAA,EACJ,EAAI,EAAA,IAAA;AAAA,EACJ,CAAG,EAAA,GAAA;AAAA,EACH,YAAc,EAAA,cAAA;AAAA,EACd,gBAAkB,EAAA,kBAAA;AAAA,EAClB,OAAS,EAAA,SAAA;AAAA,EACT,SAAW,EAAA,WAAA;AAAA,EACX,UAAY,EAAA,YAAA;AAAA,EACZ,QAAU,EAAA,UAAA;AAAA,EACV,YAAc,EAAA,cAAA;AAAA,EACd,aAAe,EAAA,eAAA;AAAA,EACf,gBAAkB,EAAA,eAAA;AAAA,EAClB,aAAe,EAAA,eAAA;AAAA,EACf,gBAAkB,EAAA,eAAA;AAAA,EAClB,iBAAmB,EAAA,mBAAA;AAAA,EACnB,KAAO,EAAA,OAAA;AAAA,EACP,SAAW,EAAA,WAAA;AAAA,EACX,YAAc,EAAA,WAAA;AAAA,EACd,YAAc,EAAA,cAAA;AAAA,EACd,SAAW,EAAA,WAAA;AAAA,EACX,YAAc,EAAA,WAAA;AAAA,EACd,WAAa,EAAA,aAAA;AAAA,EACb,cAAgB,EAAA,aAAA;AAAA,EAChB,WAAa,EAAA,aAAA;AAAA,EACb,WAAa,EAAA,aAAA;AAAA,EACb,IAAM,EAAA,MAAA;AAAA,EACN,gBAAkB,EAAA,kBAAA;AAAA,EAClB,SAAW,EAAA,WAAA;AAAA,EACX,YAAc,EAAA,cAAA;AAAA,EACd,IAAM,EAAA,MAAA;AAAA,EACN,UAAY,EAAA,YAAA;AAAA,EACZ,MAAQ,EAAA,QAAA;AAAA,EACR,OAAS,EAAA,SAAA;AAAA,EACT,QAAU,EAAA,UAAA;AAAA,EACV,KAAO,EAAA,OAAA;AAAA,EACP,MAAQ,EAAA,QAAA;AAAA,EACR,WAAa,EAAA,aAAA;AAAA,EACb,MAAQ,EAAA,QAAA;AAAA,EACR,QAAU,EAAA,UAAA;AAAA,EACV,gBAAkB,EAAA,kBAAA;AAAA,EAClB,mBAAqB,EAAA,kBAAA;AAAA,EACrB,iBAAmB,EAAA,mBAAA;AAAA,EACnB,oBAAsB,EAAA,mBAAA;AAAA,EACtB,UAAY,EAAA,YAAA;AAAA,EACZ,aAAe,EAAA,YAAA;AAAA,EACf,OAAS,EAAA,SAAA;AAAA,EACT,UAAY,EAAA,SAAA;AAAA,EACZ,UAAY,EAAA,YAAA;AAAA,EACZ,mBAAqB,EAAA,qBAAA;AAAA,EACrB,gBAAkB,EAAA,kBAAA;AAAA,EAClB,YAAc,EAAA,cAAA;AAAA,EACd,aAAe,EAAA,eAAA;AAAA,EACf,gBAAkB,EAAA,eAAA;AAAA,EAClB,MAAQ,EAAA,QAAA;AAAA,EACR,SAAW,EAAA,WAAA;AAAA,EACX,SAAW,EAAA,WAAA;AAAA,EACX,SAAW,EAAA,WAAA;AAAA,EACX,MAAQ,EAAA,QAAA;AAAA,EACR,aAAe,EAAA,eAAA;AAAA,EACf,mBAAqB,EAAA,qBAAA;AAAA,EACrB,cAAgB,EAAA,gBAAA;AAAA,EAChB,QAAU,EAAA,UAAA;AAAA,EACV,CAAG,EAAA,GAAA;AAAA,EACH,MAAQ,EAAA,QAAA;AAAA,EACR,IAAM,EAAA,MAAA;AAAA,EACN,IAAM,EAAA,MAAA;AAAA,EACN,eAAiB,EAAA,iBAAA;AAAA,EACjB,kBAAoB,EAAA,iBAAA;AAAA,EACpB,WAAa,EAAA,aAAA;AAAA,EACb,SAAW,EAAA,WAAA;AAAA,EACX,kBAAoB,EAAA,oBAAA;AAAA,EACpB,gBAAkB,EAAA,kBAAA;AAAA,EAClB,QAAU,EAAA,UAAA;AAAA,EACV,OAAS,EAAA,SAAA;AAAA,EACT,MAAQ,EAAA,QAAA;AAAA,EACR,OAAS,EAAA,SAAA;AAAA,EACT,MAAQ,EAAA,QAAA;AAAA,EACR,EAAI,EAAA,IAAA;AAAA,EACJ,EAAI,EAAA,IAAA;AAAA,EACJ,KAAO,EAAA,OAAA;AAAA,EACP,QAAU,EAAA,UAAA;AAAA,EACV,IAAM,EAAA,MAAA;AAAA,EACN,cAAgB,EAAA,gBAAA;AAAA,EAChB,iBAAmB,EAAA,gBAAA;AAAA,EACnB,KAAO,EAAA,OAAA;AAAA,EACP,OAAS,EAAA,SAAA;AAAA,EACT,gBAAkB,EAAA,kBAAA;AAAA,EAClB,gBAAkB,EAAA,kBAAA;AAAA,EAClB,KAAO,EAAA,OAAA;AAAA,EACP,YAAc,EAAA,cAAA;AAAA,EACd,WAAa,EAAA,aAAA;AAAA,EACb,YAAc,EAAA,cAAA;AAAA,EACd,KAAO,EAAA,OAAA;AAAA,EACP,KAAO,EAAA,OAAA;AAAA,EACP,WAAa,EAAA,aAAA;AAAA,EACb,SAAW,EAAA,WAAA;AAAA,EACX,YAAc,EAAA,WAAA;AAAA,EACd,WAAa,EAAA,aAAA;AAAA,EACb,cAAgB,EAAA,aAAA;AAAA,EAChB,qBAAuB,EAAA,uBAAA;AAAA,EACvB,wBAA0B,EAAA,uBAAA;AAAA,EAC1B,sBAAwB,EAAA,wBAAA;AAAA,EACxB,yBAA2B,EAAA,wBAAA;AAAA,EAC3B,MAAQ,EAAA,QAAA;AAAA,EACR,MAAQ,EAAA,QAAA;AAAA,EACR,eAAiB,EAAA,iBAAA;AAAA,EACjB,kBAAoB,EAAA,iBAAA;AAAA,EACpB,gBAAkB,EAAA,kBAAA;AAAA,EAClB,mBAAqB,EAAA,kBAAA;AAAA,EACrB,aAAe,EAAA,eAAA;AAAA,EACf,gBAAkB,EAAA,eAAA;AAAA,EAClB,cAAgB,EAAA,gBAAA;AAAA,EAChB,iBAAmB,EAAA,gBAAA;AAAA,EACnB,gBAAkB,EAAA,kBAAA;AAAA,EAClB,mBAAqB,EAAA,kBAAA;AAAA,EACrB,WAAa,EAAA,aAAA;AAAA,EACb,cAAgB,EAAA,aAAA;AAAA,EAChB,aAAe,EAAA,eAAA;AAAA,EACf,gBAAkB,EAAA,eAAA;AAAA,EAClB,8BAAgC,EAAA,gCAAA;AAAA,EAChC,wBAA0B,EAAA,0BAAA;AAAA,EAC1B,YAAc,EAAA,cAAA;AAAA,EACd,cAAgB,EAAA,gBAAA;AAAA,EAChB,WAAa,EAAA,aAAA;AAAA,EACb,OAAS,EAAA,SAAA;AAAA,EACT,OAAS,EAAA,SAAA;AAAA,EACT,UAAY,EAAA,YAAA;AAAA,EACZ,aAAe,EAAA,YAAA;AAAA,EACf,cAAgB,EAAA,gBAAA;AAAA,EAChB,iBAAmB,EAAA,gBAAA;AAAA,EACnB,UAAY,EAAA,YAAA;AAAA,EACZ,aAAe,EAAA,eAAA;AAAA,EACf,gBAAkB,EAAA,eAAA;AAAA,EAClB,EAAI,EAAA,IAAA;AAAA,EACJ,SAAW,EAAA,WAAA;AAAA,EACX,MAAQ,EAAA,QAAA;AAAA,EACR,EAAI,EAAA,IAAA;AAAA,EACJ,EAAI,EAAA,IAAA;AAAA,EACJ,iBAAmB,EAAA,mBAAA;AAAA,EACnB,oBAAsB,EAAA,mBAAA;AAAA,EACtB,kBAAoB,EAAA,oBAAA;AAAA,EACpB,qBAAuB,EAAA,oBAAA;AAAA,EACvB,OAAS,EAAA,SAAA;AAAA,EACT,WAAa,EAAA,aAAA;AAAA,EACb,cAAgB,EAAA,aAAA;AAAA,EAChB,YAAc,EAAA,cAAA;AAAA,EACd,eAAiB,EAAA,cAAA;AAAA,EACjB,UAAY,EAAA,YAAA;AAAA,EACZ,cAAgB,EAAA,YAAA;AAAA,EAChB,YAAc,EAAA,cAAA;AAAA,EACd,WAAa,EAAA,aAAA;AAAA,EACb,cAAgB,EAAA,aAAA;AAAA,EAChB,MAAQ,EAAA,QAAA;AAAA,EACR,YAAc,EAAA,cAAA;AAAA,EACd,eAAiB,EAAA,cAAA;AAAA,EACjB,OAAS,EAAA,SAAA;AAAA,EACT,QAAU,EAAA,UAAA;AAAA,EACV,YAAc,EAAA,UAAA;AAAA,EACd,WAAa,EAAA,aAAA;AAAA,EACb,eAAiB,EAAA,aAAA;AAAA,EACjB,WAAa,EAAA,aAAA;AAAA,EACb,eAAiB,EAAA,aAAA;AAAA,EACjB,QAAU,EAAA,UAAA;AAAA,EACV,WAAa,EAAA,UAAA;AAAA,EACb,YAAc,EAAA,cAAA;AAAA,EACd,eAAiB,EAAA,cAAA;AAAA,EACjB,OAAS,EAAA,SAAA;AAAA,EACT,UAAY,EAAA,YAAA;AAAA,EACZ,UAAY,EAAA,YAAA;AAAA,EACZ,aAAe,EAAA,eAAA;AAAA,EACf,gBAAkB,EAAA,eAAA;AAAA,EAClB,KAAO,EAAA,OAAA;AAAA,EACP,MAAQ,EAAA,QAAA;AAAA,EACR,WAAa,EAAA,aAAA;AAAA,EACb,cAAgB,EAAA,aAAA;AAAA,EAChB,WAAa,EAAA,aAAA;AAAA,EACb,cAAgB,EAAA,aAAA;AAAA,EAChB,EAAI,EAAA,IAAA;AAAA,EACJ,EAAI,EAAA,IAAA;AAAA,EACJ,CAAG,EAAA,GAAA;AAAA,EACH,gBAAkB,EAAA,kBAAA;AAAA,EAClB,OAAS,EAAA,SAAA;AAAA,EACT,UAAY,EAAA,SAAA;AAAA,EACZ,YAAc,EAAA,cAAA;AAAA,EACd,eAAiB,EAAA,cAAA;AAAA,EACjB,YAAc,EAAA,cAAA;AAAA,EACd,eAAiB,EAAA,cAAA;AAAA,EACjB,SAAW,EAAA,WAAA;AAAA,EACX,YAAc,EAAA,WAAA;AAAA,EACd,SAAW,EAAA,WAAA;AAAA,EACX,YAAc,EAAA,WAAA;AAAA,EACd,SAAW,EAAA,WAAA;AAAA,EACX,YAAc,EAAA,WAAA;AAAA,EACd,UAAY,EAAA,YAAA;AAAA,EACZ,aAAe,EAAA,YAAA;AAAA,EACf,SAAW,EAAA,WAAA;AAAA,EACX,YAAc,EAAA,WAAA;AAAA,EACd,OAAS,EAAA,SAAA;AAAA,EACT,UAAY,EAAA,SAAA;AAAA,EACZ,OAAS,EAAA,SAAA;AAAA,EACT,UAAY,EAAA,SAAA;AAAA,EACZ,KAAO,EAAA,OAAA;AAAA,EACP,WAAa,EAAA,UAAA;AAAA,EACb,UAAY,EAAA,YAAA;AAAA,EACZ,aAAe,EAAA,YAAA;AAAA,EACf,QAAU,EAAA,UAAA;AAAA,EACV,EAAI,EAAA,IAAA;AAAA,EACJ,EAAI,EAAA,IAAA;AAAA,EACJ,CAAG,EAAA,GAAA;AAAA,EACH,gBAAkB,EAAA,kBAAA;AAAA,EAClB,CAAG,EAAA,GAAA;AAAA,EACH,UAAY,EAAA,YAAA;AACd,CAAA,CAAA;AAEO,MAAM,yBAA4B,GAAA;AAAA,EACvC,KAAO,EAAA;AAAA,IACL,OAAS,EAAA,gBAAA;AAAA,IACT,KAAO,EAAA,cAAA;AAAA,IACP,SAAW,EAAA,WAAA;AAAA,GACb;AAAA,EACA,IAAM,EAAA;AAAA,IACJ,OAAS,EAAA,SAAA;AAAA,GACX;AACF,CAAA,CAAA;AAGO,MAAM,wBAAmD,GAAA;AAAA,EAC9D,CAAG,EAAA,GAAA;AAAA,EACH,QAAU,EAAA,UAAA;AAAA,EACV,WAAa,EAAA,aAAA;AAAA,EACb,YAAc,EAAA,cAAA;AAAA,EACd,OAAS,EAAA,SAAA;AAAA,EACT,YAAc,EAAA,cAAA;AAAA,EACd,aAAe,EAAA,eAAA;AAAA,EACf,gBAAkB,EAAA,kBAAA;AAAA,EAClB,KAAO,EAAA,OAAA;AAAA,EACP,MAAQ,EAAA,QAAA;AAAA,EACR,MAAQ,EAAA,QAAA;AAAA,EACR,QAAU,EAAA,UAAA;AAAA,EACV,eAAiB,EAAA,cAAA;AAAA,EACjB,MAAQ,EAAA,QAAA;AAAA,EACR,IAAM,EAAA,MAAA;AAAA,EACN,IAAM,EAAA,MAAA;AAAA,EACN,OAAS,EAAA,SAAA;AAAA,EACT,OAAS,EAAA,SAAA;AAAA,EACT,OAAS,EAAA,SAAA;AAAA,EACT,aAAe,EAAA,eAAA;AAAA,EACf,mBAAqB,EAAA,qBAAA;AAAA,EACrB,WAAa,EAAA,aAAA;AAAA,EACb,gBAAkB,EAAA,kBAAA;AAAA,EAClB,iBAAmB,EAAA,mBAAA;AAAA,EACnB,iBAAmB,EAAA,mBAAA;AAAA,EACnB,cAAgB,EAAA,gBAAA;AAAA,EAChB,YAAc,EAAA,cAAA;AAAA,EACd,OAAS,EAAA,SAAA;AAAA,EACT,OAAS,EAAA,SAAA;AAAA,EACT,OAAS,EAAA,SAAA;AAAA,EACT,OAAS,EAAA,SAAA;AAAA,EACT,OAAS,EAAA,SAAA;AAAA,EACT,cAAgB,EAAA,gBAAA;AAAA,EAChB,OAAS,EAAA,SAAA;AAAA,EACT,OAAS,EAAA,SAAA;AAAA,EACT,WAAa,EAAA,aAAA;AAAA,EACb,YAAc,EAAA,cAAA;AAAA,EACd,QAAU,EAAA,UAAA;AAAA,EACV,YAAc,EAAA,cAAA;AAAA,EACd,kBAAoB,EAAA,oBAAA;AAAA,EACpB,WAAa,EAAA,aAAA;AAAA,EACb,MAAQ,EAAA,QAAA;AAAA,EACR,YAAc,EAAA,cAAA;AAAA,EACd,MAAQ,EAAA,QAAA;AAAA,EACR,IAAM,EAAA,MAAA;AAAA,EACN,WAAa,EAAA,UAAA;AAAA,EACb,kBAAoB,EAAA,gBAAA;AAAA,EACpB,gBAAkB,EAAA,cAAA;AAAA,EAClB,eAAiB,EAAA,aAAA;AAAA,EACjB,eAAiB,EAAA,aAAA;AAAA,EACjB,aAAe,EAAA,eAAA;AAAA,EACf,CAAG,EAAA,GAAA;AAAA,EACH,KAAO,EAAA,OAAA;AAAA,EACP,QAAU,EAAA,UAAA;AAAA,EACV,KAAO,EAAA,OAAA;AAAA,EACP,SAAW,EAAA,WAAA;AAAA,EACX,KAAO,EAAA,OAAA;AAAA,EACP,MAAQ,EAAA,QAAA;AAAA,EACR,KAAO,EAAA,OAAA;AAAA,EACP,IAAM,EAAA,MAAA;AAAA,EACN,cAAgB,EAAA,gBAAA;AAAA,EAChB,MAAQ,EAAA,QAAA;AAAA,EACR,IAAM,EAAA,MAAA;AAAA,EACN,IAAM,EAAA,MAAA;AAAA,EACN,YAAc,EAAA,cAAA;AAAA,EACd,SAAW,EAAA,WAAA;AAAA,EACX,OAAS,EAAA,SAAA;AAAA,EACT,QAAU,EAAA,UAAA;AAAA,EACV,eAAiB,EAAA,cAAA;AAAA,EACjB,KAAO,EAAA,OAAA;AAAA,EACP,IAAM,EAAA,MAAA;AAAA,EACN,OAAS,EAAA,SAAA;AAAA,EACT,OAAS,EAAA,SAAA;AAAA,EACT,QAAU,EAAA,UAAA;AAAA,EACV,cAAgB,EAAA,gBAAA;AAAA,EAChB,IAAM,EAAA,MAAA;AAAA,EACN,MAAQ,EAAA,QAAA;AAAA,EACR,GAAK,EAAA,KAAA;AAAA,EACL,UAAY,EAAA,YAAA;AAAA,EACZ,IAAM,EAAA,MAAA;AAAA,EACN,KAAO,EAAA,OAAA;AAAA,EACP,GAAK,EAAA,KAAA;AAAA,EACL,MAAQ,EAAA,QAAA;AAAA,EACR,MAAQ,EAAA,QAAA;AAAA,EACR,IAAM,EAAA,MAAA;AAAA,EACN,QAAU,EAAA,UAAA;AAAA,EACV,KAAO,EAAA,OAAA;AAAA,EACP,IAAM,EAAA,MAAA;AAAA,EACN,KAAO,EAAA,OAAA;AAAA,EACP,OAAS,EAAA,SAAA;AAAA,EACT,GAAK,EAAA,KAAA;AAAA,EACL,KAAO,EAAA,OAAA;AAAA,EACP,IAAM,EAAA,MAAA;AAAA,EACN,KAAO,EAAA,OAAA;AACT,CAAA;;AC5kBA,MAAM,oBAAA,GAAuB,CAAC,QAAqB,KAAA;AACjD,EAAA,MAAM,CAAC,IAAS,EAAA,GAAA,KAAK,CAAI,GAAA,QAAA,CAAS,MAAM,GAAG,CAAA,CAAA;AAC3C,EAAA,OAAO,GAAG,IAAQ,CAAA,CAAA,EAAA,KAAA,CAAM,IAAK,CAAA,EAAE,EAAE,WAAY,EAAA,CAAA,CAAA,CAAA;AAC/C,CAAA,CAAA;AAEA,MAAM,MAAA,GAAS,CAAC,GAAA,EAAa,IAAsB,KAAA;AACjD,EAAM,MAAA,YAAA,GAAe,IAAI,WAAY,EAAA,CAAA;AACrC,EAAA,MAAM,yBAEJ,yBAA0B,CAAA,IAAA,CAAK,IAE/B,CAAA,IAAA,yBAAA,CAA0B,KAAK,IAAM,CAAA,CAAA,YAAA,CAAA,CAAA;AAEvC,EAAA,MAAM,kBAAkB,iBAAkB,CAAA,YAAA,CAAA,CAAA;AAE1C,EAAA,IAAI,0BAA0B,eAAiB,EAAA;AAC7C,IAAO,OAAAA,YAAA,CAAE,aAAc,CAAA,sBAAA,IAA0B,eAAe,CAAA,CAAA;AAAA,GAClE;AAEA,EAAM,MAAA,QAAA,GAAW,UAAU,GAAG,CAAA,CAAA;AAE9B,EAAI,IAAA,QAAA,CAAS,UAAW,CAAA,OAAO,CAAG,EAAA;AAChC,IAAA,OAAOA,YAAE,CAAA,aAAA,CAAc,oBAAqB,CAAA,QAAQ,CAAC,CAAA,CAAA;AAAA,GACvD;AAEA,EAAI,IAAA,QAAA,CAAS,UAAW,CAAA,OAAO,CAAG,EAAA;AAChC,IAAO,OAAAA,YAAA,CAAE,cAAc,QAAQ,CAAA,CAAA;AAAA,GACjC;AAEA,EAAO,OAAAA,YAAA,CAAE,cAAc,GAAG,CAAA,CAAA;AAC5B,CAAA,CAAA;AAEA,MAAM,QAAA,GAAW,CAAC,GAAA,EAAa,KAAsC,KAAA;AAEnE,EAAI,IAAA,KAAA,CAAM,OAAQ,CAAA,KAAK,CAAG,EAAA;AACxB,IAAA,OAAOA,aAAE,aAAc,CAAA,aAAA,CAAc,MAAM,IAAK,CAAA,GAAG,CAAC,CAAC,CAAA,CAAA;AAAA,GACvD;AAEA,EAAA,IAAI,QAAQ,OAAS,EAAA;AACnB,IAAA,OAAOA,YAAE,CAAA,sBAAA,CAAuB,mBAAoB,CAAA,KAAe,CAAC,CAAA,CAAA;AAAA,GACtE;AAEA,EAAA,IAAI,OAAO,KAAA,KAAU,QAAY,IAAA,SAAA,CAAU,KAAK,CAAG,EAAA;AACjD,IAAA,OAAOA,aAAE,sBAAuB,CAAAA,YAAA,CAAE,eAAe,MAAO,CAAA,KAAK,CAAC,CAAC,CAAA,CAAA;AAAA,GACjE;AAEA,EAAA,OAAOA,YAAE,CAAA,aAAA,CAAc,aAAc,CAAA,KAAK,CAAC,CAAA,CAAA;AAC7C,CAAA,CAAA;AAEa,MAAA,aAAA,GAAgB,CAAC,IAAwC,KAAA;AACpE,EAAA,IAAI,CAAC,IAAK,CAAA,UAAA;AAAY,IAAA,OAAO,EAAC,CAAA;AAC9B,EAAA,MAAM,IAAO,GAAA,MAAA,CAAO,IAAK,CAAA,IAAA,CAAK,UAAU,CAAA,CAAA;AACxC,EAAA,MAAM,aAAa,EAAC,CAAA;AACpB,EAAA,IAAI,KAAQ,GAAA,CAAA,CAAA,CAAA;AAEZ,EAAO,OAAA,EAAE,KAAQ,GAAA,IAAA,CAAK,MAAQ,EAAA;AAC5B,IAAA,MAAM,MAAM,IAAK,CAAA,KAAA,CAAA,CAAA;AACjB,IAAM,MAAA,KAAA,GAAQ,KAAK,UAAW,CAAA,GAAA,CAAA,CAAA;AAC9B,IAAM,MAAA,SAAA,GAAYA,YAAE,CAAA,YAAA,CAAa,MAAO,CAAA,GAAA,EAAK,IAAI,CAAG,EAAA,QAAA,CAAS,GAAK,EAAA,KAAK,CAAC,CAAA,CAAA;AACxE,IAAA,UAAA,CAAW,KAAK,SAAS,CAAA,CAAA;AAAA,GAC3B;AAEA,EAAO,OAAA,UAAA,CAAA;AACT,CAAA;;AC7Da,MAAA,IAAA,GAAO,CAAC,CAAY,EAAA,IAAA,KAE/BA,aAAE,OAAQ,CAAA,GAAA,CAAI,CAAG,EAAA,IAAI,CAAC,CAAA,CAAA;AAEjB,MAAM,OAAU,GAAA,CACrB,CACA,EAAA,IAAA,EACA,MACoC,KAAA;AACpC,EAAA,IAAI,MAAO,CAAA,IAAA,KAAS,MAAU,IAAA,CAAC,IAAK,CAAA,KAAA;AAAO,IAAO,OAAA,IAAA,CAAA;AAElD,EAAM,MAAA,UAAA,GAAaA,aAAE,kBAAmB,EAAA,CAAA;AACxC,EAAAA,YAAA,CAAE,UAAW,CAAA,UAAA,EAAY,OAAS,EAAA,IAAA,CAAK,KAAK,CAAA,CAAA;AAC5C,EAAO,OAAAA,YAAA,CAAE,uBAAuB,UAAU,CAAA,CAAA;AAC5C,CAAA,CAAA;AAEA,MAAM,WAAc,GAAA,OAAA,CAAA;AAEb,MAAM,IAAO,GAAA,CAClB,CACA,EAAA,IAAA,EACA,MACoC,KAAA;AACpC,EAAA,IAAI,OAAO,IAAS,KAAA,MAAA;AAAQ,IAAO,OAAA,IAAA,CAAA;AACnC,EAAA,IAAI,OAAO,IAAK,CAAA,KAAA,KAAU,YAAY,WAAY,CAAA,IAAA,CAAK,KAAK,KAAK,CAAA;AAC/D,IAAO,OAAA,IAAA,CAAA;AAET,EAAA,OAAOA,YAAE,CAAA,sBAAA;AAAA,IACPA,aAAE,aAAc,CAAAC,kBAAA,CAAU,OAAO,IAAK,CAAA,KAAK,CAAC,CAAC,CAAA;AAAA,GAC/C,CAAA;AACF,CAAA,CAAA;AAEO,MAAM,OAAU,GAAA,CACrB,CACA,EAAA,IAAA,EACA,MACgD,KAAA;AAChD,EAAA,IAAI,CAAC,IAAK,CAAA,OAAA;AAAS,IAAO,OAAA,IAAA,CAAA;AAE1B,EAAM,MAAA,QAAA,GAAW,GAAI,CAAA,CAAA,EAAG,IAAI,CAAA,CAAA;AAC5B,EAAM,MAAA,WAAA,GAAc,SAAS,MAAW,KAAA,CAAA,CAAA;AAExC,EAAA,MAAM,IAAO,GAAA,wBAAA,CAAyB,IAAK,CAAA,OAAA,CAAA,IAAY,IAAK,CAAA,OAAA,CAAA;AAE5D,EAAA,MAAM,iBAAiBD,YAAE,CAAA,iBAAA;AAAA,IACvBA,YAAA,CAAE,cAAc,IAAI,CAAA;AAAA,IACpB,cAAc,IAAI,CAAA;AAAA,IAClB,WAAA;AAAA,GACF,CAAA;AAEA,EAAM,MAAA,cAAA,GAAiB,CAAC,WACpB,GAAAA,YAAA,CAAE,kBAAkBA,YAAE,CAAA,aAAA,CAAc,IAAI,CAAC,CACzC,GAAA,IAAA,CAAA;AAEJ,EAAA,MAAM,UAAa,GAAAA,YAAA,CAAE,UAAW,CAAA,cAAA,EAAgB,gBAAgB,QAAQ,CAAA,CAAA;AAExE,EAAI,IAAA,MAAA,CAAO,SAAS,MAAQ,EAAA;AAC1B,IAAO,OAAAA,YAAA,CAAE,oBAAoB,UAAU,CAAA,CAAA;AAAA,GACzC;AAEA,EAAO,OAAA,UAAA,CAAA;AACT,CAAA;;;;;;;;;;ACnEa,MAAA,OAAA,GAAU,EAAE,QAAS,EAAA;;ACGlC,MAAM,UAAa,GAAA,CAAC,IAA8B,KAAA,IAAA,CAAK,SAAS,IAAI;;;;"}