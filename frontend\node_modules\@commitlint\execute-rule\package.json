{"name": "@commitlint/execute-rule", "type": "module", "version": "19.8.1", "description": "Lint your commit messages", "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib/"], "scripts": {"deps": "dep-check", "pkg": "pkg-check"}, "engines": {"node": ">=v18"}, "repository": {"type": "git", "url": "https://github.com/conventional-changelog/commitlint.git", "directory": "@commitlint/execute-rule"}, "bugs": {"url": "https://github.com/conventional-changelog/commitlint/issues"}, "homepage": "https://commitlint.js.org/", "keywords": ["conventional-changelog", "commitlint", "library", "core"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@commitlint/utils": "^19.8.1"}, "gitHead": "3c302008cabeb0b08cd246b2417a51a9d745a918"}