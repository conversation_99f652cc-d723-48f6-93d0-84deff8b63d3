{"name": "@commitlint/is-ignored", "type": "module", "version": "19.8.1", "description": "Lint your commit messages", "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib/"], "scripts": {"deps": "dep-check", "pkg": "pkg-check"}, "engines": {"node": ">=v18"}, "repository": {"type": "git", "url": "https://github.com/conventional-changelog/commitlint.git", "directory": "@commitlint/is-ignored"}, "bugs": {"url": "https://github.com/conventional-changelog/commitlint/issues"}, "homepage": "https://commitlint.js.org/", "keywords": ["conventional-changelog", "commitlint", "library", "core"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@commitlint/parse": "^19.8.1", "@commitlint/test": "^19.8.1", "@commitlint/utils": "^19.8.1", "@types/semver": "^7.5.7"}, "dependencies": {"@commitlint/types": "^19.8.1", "semver": "^7.6.0"}, "gitHead": "3c302008cabeb0b08cd246b2417a51a9d745a918"}