{"version": 3, "file": "polyfill-utils.d.ts", "sourceRoot": "", "sources": ["../src/polyfill-utils.ts"], "names": [], "mappings": "AAGA,OAAO,EACL,8BAA8B,EAC9B,wBAAwB,EACzB,MAAM,gBAAgB,CAAC;AAExB;;;GAGG;AACH,wBAAgB,QAAQ,CAAC,CAAC,EACxB,GAAG,EAAE,CAAC,GACL,CAAC,SAAS,IAAI,GAAG,KAAK,GAAG,CAAC,SAAS,SAAS,GAAG,KAAK,GAAG,CAAC,CAK1D;AAED;;GAEG;AACH,wBAAgB,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,MAAM,CAM3C;AAED;;;;;;;GAOG;AACH,wBAAgB,SAAS,CAAC,CAAC,SAAS,MAAM,EAAE,CAAC,SAAS,MAAM,CAAC,EAAE,CAAC,EAC9D,IAAI,EAAE,CAAC,EACP,IAAI,EAAE,CAAC,EACP,IAAI,EAAE,QAAQ,GAAG,SAAS,EAC1B,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,SAAS,EAC1B,QAAQ,EAAE,CAAC,GACV,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,GAAG,CAAC,CAmB9B;AAED;;;;;;GAMG;AACH,wBAAgB,mBAAmB,CACjC,GAAG,EAAE,GAAG,EACR,GAAG,EAAE,MAAM,EACX,GAAG,EAAE,MAAM,EACX,QAAQ,EAAE,MAAM,UAUjB;AAED;;;;;;;GAOG;AAEH,wBAAgB,eAAe,CAAC,CAAC,SAAS,MAAM,EAAE,CAAC,SAAS,MAAM,CAAC,EACjE,OAAO,EAAE,CAAC,EACV,QAAQ,EAAE,CAAC,EACX,OAAO,EAAE,MAAM,EACf,OAAO,EAAE,MAAM,EACf,QAAQ,EAAE,MAAM,GACf,MAAM,CAGR;AAED,wBAAgB,gBAAgB,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAOrE;AAED,wBAAgB,sBAAsB,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAU3E;AAED,wBAAgB,eAAe,CAC7B,QAAQ,SAAS,MAAM,EACvB,QAAQ,SAAS,MAAM,EACvB,KAAK,SAAS,MAAM,QAAQ,EAE5B,GAAG,EAAE,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAChC,EAAE,EAAE,QAAQ,EACZ,KAAK,EAAE,KAAK,EACZ,KAAK,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,QAOpC;AAED,wBAAgB,qBAAqB,CACnC,QAAQ,SAAS,MAAM,EACvB,QAAQ,SAAS,MAAM,EACvB,CAAC,SAAS,MAAM,QAAQ,EAExB,GAAG,EAAE,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAChC,EAAE,EAAE,QAAQ,EACZ,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAKtC;AAED,wBAAgB,eAAe,CAC7B,QAAQ,SAAS,MAAM,EACvB,QAAQ,SAAS,MAAM,EACvB,KAAK,SAAS,MAAM,QAAQ,EAE5B,GAAG,EAAE,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAChC,EAAE,EAAE,QAAQ,EACZ,KAAK,EAAE,KAAK,GACX,QAAQ,CAAC,KAAK,CAAC,CAEjB;AAED,wBAAgB,qBAAqB,CACnC,QAAQ,SAAS,MAAM,EACvB,QAAQ,SAAS,MAAM,EACvB,KAAK,SAAS,MAAM,QAAQ,EAE5B,GAAG,EAAE,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAChC,EAAE,EAAE,QAAQ,EACZ,GAAG,MAAM,EAAE,KAAK,EAAE,GACjB,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CASvB;AAED,MAAM,WAAW,WAAW;IAC1B,IAAI,EAAE,SAAS,CAAC;IAChB,KAAK,EAAE,MAAM,CAAC;CACf;AAED,wBAAgB,aAAa,CAC3B,WAAW,EAAE,WAAW,GAAG;IAAC,IAAI,EAAE,MAAM,CAAC;IAAC,KAAK,CAAC,EAAE,MAAM,CAAA;CAAC,GACxD,WAAW,IAAI,WAAW,CAE5B;AAED,wBAAgB,gBAAgB,CAAC,OAAO,EAAE,MAAM;;;;;;KA6B/C;AAED;;;;;;;GAOG;AACH,wBAAgB,2BAA2B,CACzC,OAAO,SAAS,MAAM,EACtB,cAAc,SAAS,8BAA8B,EAErD,eAAe,EAAE,OAAO,CAAC,OAAO,EAAE,cAAc,CAAC,EACjD,OAAO,EAAE,OAAO,EAChB,IAAI,EAAE,wBAAwB,EAC9B,WAAW,EAAE,MAAM,EACnB,WAAW,EAAE,MAAM,QAkDpB;AAED,wBAAgB,QAAQ,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,WAYtC;AAYD;;;GAGG;AACH,wBAAgB,wBAAwB,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CASlE"}