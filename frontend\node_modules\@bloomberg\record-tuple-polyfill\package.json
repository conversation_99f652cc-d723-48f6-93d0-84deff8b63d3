{"name": "@bloomberg/record-tuple-polyfill", "version": "0.0.4", "description": "A polyfill for the Record and Tuple Stage 2 ECMAScript proposal.", "main": "lib/index.umd.js", "module": "lib/index.esm.js", "files": ["lib/*.js"], "scripts": {"clean": "rm -rf ./lib"}, "repository": {"type": "git", "url": "https://github.com/bloomberg/record-tuple-polyfill"}, "keywords": ["record", "tuple", "record and tuple", "polyfill", "ecmascript"], "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>"], "license": "Apache-2.0", "publishConfig": {"access": "public"}}