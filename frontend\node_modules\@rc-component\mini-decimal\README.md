# @rc-component/mini-decimal

React 18 supported Portal Component.

[![NPM version][npm-image]][npm-url] [![dumi](https://img.shields.io/badge/docs%20by-dumi-blue?style=flat-square)](https://github.com/umijs/dumi) [![build status][github-actions-image]][github-actions-url] [![Codecov][codecov-image]][codecov-url] [![npm download][download-image]][download-url]

[npm-image]: http://img.shields.io/npm/v/@rc-component/mini-decimal.svg?style=flat-square
[npm-url]: http://npmjs.org/package/@rc-component/mini-decimal
[github-actions-image]: https://github.com/react-component/mini-decimal/workflows/CI/badge.svg
[github-actions-url]: https://github.com/react-component/mini-decimal/actions
[codecov-image]: https://img.shields.io/codecov/c/github/react-component/mini-decimal/master.svg?style=flat-square
[codecov-url]: https://codecov.io/gh/react-component/mini-decimal/branch/master
[download-image]: https://img.shields.io/npm/dm/@rc-component/mini-decimal.svg?style=flat-square
[download-url]: https://npmjs.org/package/@rc-component/mini-decimal

## Development

```bash
npm install
npm test
```
